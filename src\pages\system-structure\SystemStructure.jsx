"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "../../components/ui/card.jsx"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Database, Server, Globe, Shield, Cpu, HardDrive } from "lucide-react"

const systemComponents = [
  {
    name: "Frontend Application",
    type: "React.js + Vite",
    status: "Active",
    description: "User interface built with React.js and Vite for fast development",
    dependencies: ["React Router", "Recharts", "Tailwind CSS", "Radix UI"],
  },
  {
    name: "API Gateway",
    type: "Node.js/Express",
    status: "Active",
    description: "RESTful API handling all client-server communication",
    dependencies: ["Express.js", "CORS", "Rate Limiting", "Authentication"],
  },
  {
    name: "Database",
    type: "PostgreSQL",
    status: "Active",
    description: "Primary database for storing business intelligence data",
    dependencies: ["Connection Pooling", "Backup System", "Indexing"],
  },
  {
    name: "Analytics Engine",
    type: "Custom",
    status: "Active",
    description: "Data processing and analytics computation engine",
    dependencies: ["Data Aggregation", "Real-time Processing", "Caching"],
  },
]

const databaseSchema = [
  { table: "customers", records: "15,234", description: "Customer information and profiles" },
  { table: "transactions", records: "89,567", description: "Transaction history and details" },
  { table: "customer_journey", records: "45,123", description: "Customer journey tracking data" },
  { table: "analytics_cache", records: "12,890", description: "Cached analytics results" },
  { table: "users", records: "156", description: "System users and permissions" },
  { table: "masters_data", records: "2,345", description: "Reference data and configurations" },
]

const apiEndpoints = [
  { endpoint: "/api/auth", methods: ["POST"], description: "Authentication and authorization" },
  { endpoint: "/api/customers", methods: ["GET", "POST", "PUT", "DELETE"], description: "Customer management" },
  { endpoint: "/api/analytics", methods: ["GET"], description: "Analytics data retrieval" },
  { endpoint: "/api/reports", methods: ["GET", "POST"], description: "Report generation and management" },
  { endpoint: "/api/masters", methods: ["GET", "POST", "PUT", "DELETE"], description: "Master data management" },
]

const systemMetrics = [
  { metric: "Uptime", value: "99.9%", status: "excellent" },
  { metric: "Response Time", value: "< 200ms", status: "good" },
  { metric: "Database Size", value: "2.3 GB", status: "normal" },
  { metric: "Active Users", value: "156", status: "normal" },
  { metric: "API Calls/Day", value: "45,678", status: "normal" },
  { metric: "Error Rate", value: "0.1%", status: "excellent" },
]

export default function SystemStructurePage() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Structure</h1>
          <p className="text-muted-foreground">Overview of the M4G Business Intelligence system architecture</p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="api">API Structure</TabsTrigger>
          <TabsTrigger value="metrics">System Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {systemComponents.map((component, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      {component.name === "Frontend Application" && <Globe className="h-5 w-5" />}
                      {component.name === "API Gateway" && <Server className="h-5 w-5" />}
                      {component.name === "Database" && <Database className="h-5 w-5" />}
                      {component.name === "Analytics Engine" && <Cpu className="h-5 w-5" />}
                      <span>{component.name}</span>
                    </CardTitle>
                    <Badge variant={component.status === "Active" ? "default" : "secondary"}>{component.status}</Badge>
                  </div>
                  <CardDescription>{component.type}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">{component.description}</p>
                  <div>
                    <h4 className="text-sm font-medium mb-2">Dependencies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {component.dependencies.map((dep, depIndex) => (
                        <Badge key={depIndex} variant="outline" className="text-xs">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Database Schema</span>
              </CardTitle>
              <CardDescription>Overview of database tables and their contents</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {databaseSchema.map((table, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <HardDrive className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{table.table}</div>
                        <div className="text-sm text-muted-foreground">{table.description}</div>
                      </div>
                    </div>
                    <Badge variant="secondary">{table.records} records</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="h-5 w-5" />
                <span>API Endpoints</span>
              </CardTitle>
              <CardDescription>Available API endpoints and their methods</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiEndpoints.map((api, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-mono text-sm font-medium">{api.endpoint}</div>
                      <div className="text-sm text-muted-foreground">{api.description}</div>
                    </div>
                    <div className="flex space-x-2">
                      {api.methods.map((method, methodIndex) => (
                        <Badge key={methodIndex} variant="outline" className="text-xs">
                          {method}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>System Performance Metrics</span>
              </CardTitle>
              <CardDescription>Real-time system health and performance indicators</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {systemMetrics.map((metric, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">{metric.metric}</span>
                      <Badge
                        variant={
                          metric.status === "excellent" ? "default" : metric.status === "good" ? "secondary" : "outline"
                        }
                      >
                        {metric.status}
                      </Badge>
                    </div>
                    <div className="text-2xl font-bold">{metric.value}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
