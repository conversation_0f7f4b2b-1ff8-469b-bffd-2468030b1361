// API Response Types and Interfaces for React.js

export const createApiResponse = (success = false, data = null, error = null, message = null, pagination = null) => ({
  success,
  data,
  error,
  message,
  pagination,
})

export const createPagination = (page = 1, limit = 10, total = 0, totalPages = 0) => ({
  page,
  limit,
  total,
  totalPages,
})

export const createAuthResponse = (user = null, token = null) => ({
  user,
  token,
})

// Default API request options
export const DEFAULT_API_OPTIONS = {
  method: "GET",
  headers: {
    "Content-Type": "application/json",
  },
}

// HTTP Methods
export const HTTP_METHODS = {
  GET: "GET",
  POST: "POST",
  PUT: "PUT",
  DELETE: "DELETE",
  PATCH: "PATCH",
}

// API Status Codes
export const API_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
}
