"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Input } from "../../components/ui/input.jsx"
import { Search, Book, FileText, Video, Download } from "lucide-react"

const documentationSections = [
  {
    id: "getting-started",
    title: "Getting Started",
    description: "Learn the basics of using the M4G Business Intelligence platform",
    articles: [
      { title: "Platform Overview", type: "guide", readTime: "5 min" },
      { title: "First Login", type: "tutorial", readTime: "3 min" },
      { title: "Dashboard Navigation", type: "guide", readTime: "7 min" },
      { title: "User Roles & Permissions", type: "reference", readTime: "10 min" },
    ],
  },
  {
    id: "analytics",
    title: "Analytics & Reports",
    description: "Understanding analytics, creating reports, and interpreting data",
    articles: [
      { title: "Analytics Dashboard", type: "guide", readTime: "8 min" },
      { title: "Creating Custom Reports", type: "tutorial", readTime: "15 min" },
      { title: "Data Visualization", type: "guide", readTime: "12 min" },
      { title: "Exporting Data", type: "tutorial", readTime: "5 min" },
    ],
  },
  {
    id: "customer-management",
    title: "Customer Management",
    description: "Managing customers, tracking journeys, and analyzing behavior",
    articles: [
      { title: "Adding Customers", type: "tutorial", readTime: "6 min" },
      { title: "Customer Journey Mapping", type: "guide", readTime: "20 min" },
      { title: "Customer Segmentation", type: "guide", readTime: "15 min" },
      { title: "Customer Analytics", type: "reference", readTime: "12 min" },
    ],
  },
  {
    id: "masters-data",
    title: "Master Data Management",
    description: "Managing reference data, configurations, and system settings",
    articles: [
      { title: "Managing Companies", type: "tutorial", readTime: "8 min" },
      { title: "City & Location Data", type: "guide", readTime: "6 min" },
      { title: "Medium & Channels", type: "guide", readTime: "10 min" },
      { title: "Data Import/Export", type: "tutorial", readTime: "12 min" },
    ],
  },
]

const videoTutorials = [
  { title: "Platform Introduction", duration: "10:30", thumbnail: "/placeholder.svg?height=120&width=200" },
  { title: "Creating Your First Report", duration: "15:45", thumbnail: "/placeholder.svg?height=120&width=200" },
  { title: "Customer Journey Analysis", duration: "18:20", thumbnail: "/placeholder.svg?height=120&width=200" },
  { title: "Advanced Analytics", duration: "22:15", thumbnail: "/placeholder.svg?height=120&width=200" },
]

const apiDocumentation = [
  { endpoint: "GET /api/customers", description: "Retrieve customer list", status: "Active" },
  { endpoint: "POST /api/customers", description: "Create new customer", status: "Active" },
  { endpoint: "GET /api/analytics/dashboard", description: "Get dashboard data", status: "Active" },
  { endpoint: "GET /api/reports/generate", description: "Generate custom report", status: "Active" },
  { endpoint: "POST /api/customer-journey", description: "Create customer journey", status: "Beta" },
]

export default function DocumentationPage() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Documentation</h1>
          <p className="text-muted-foreground">Learn how to use the M4G Business Intelligence platform</p>
        </div>
        <Button>
          <Download className="h-4 w-4 mr-2" />
          Download PDF
        </Button>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input placeholder="Search documentation..." className="pl-10" />
      </div>

      <Tabs defaultValue="guides" className="space-y-6">
        <TabsList>
          <TabsTrigger value="guides">Guides</TabsTrigger>
          <TabsTrigger value="videos">Video Tutorials</TabsTrigger>
          <TabsTrigger value="api">API Reference</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
        </TabsList>

        <TabsContent value="guides" className="space-y-6">
          {documentationSections.map((section) => (
            <Card key={section.id}>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Book className="h-5 w-5" />
                  <span>{section.title}</span>
                </CardTitle>
                <CardDescription>{section.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {section.articles.map((article, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 cursor-pointer"
                    >
                      <div className="flex items-center space-x-3">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{article.title}</div>
                          <div className="text-sm text-muted-foreground">{article.readTime}</div>
                        </div>
                      </div>
                      <Badge variant="outline">{article.type}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="videos" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Video className="h-5 w-5" />
                <span>Video Tutorials</span>
              </CardTitle>
              <CardDescription>Step-by-step video guides to help you get started</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {videoTutorials.map((video, index) => (
                  <div key={index} className="space-y-3">
                    <div className="relative">
                      <img
                        src={video.thumbnail || "/placeholder.svg"}
                        alt={video.title}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                        {video.duration}
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium">{video.title}</h3>
                      <p className="text-sm text-muted-foreground">Duration: {video.duration}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Reference</CardTitle>
              <CardDescription>Complete API documentation for developers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiDocumentation.map((api, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-mono text-sm font-medium">{api.endpoint}</div>
                      <div className="text-sm text-muted-foreground">{api.description}</div>
                    </div>
                    <Badge variant={api.status === "Active" ? "default" : "secondary"}>{api.status}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="faq" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Common questions and answers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="font-medium mb-2">How do I reset my password?</h3>
                  <p className="text-sm text-muted-foreground">
                    You can reset your password by clicking the "Forgot Password" link on the login page or contacting
                    your administrator.
                  </p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">How do I export data?</h3>
                  <p className="text-sm text-muted-foreground">
                    Most data tables have an export button that allows you to download data in CSV, Excel, or PDF
                    format.
                  </p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Can I customize the dashboard?</h3>
                  <p className="text-sm text-muted-foreground">
                    Yes, you can customize your dashboard by adding, removing, or rearranging widgets based on your
                    preferences.
                  </p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">How often is the data updated?</h3>
                  <p className="text-sm text-muted-foreground">
                    Data is updated in real-time for most metrics. Some reports may have a slight delay depending on the
                    data source.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
