"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Label } from "../../components/ui/label.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Calendar, BarChart3, Download, TrendingUp, Users } from "lucide-react"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts"
import { PageHeader } from "../../components/page-header.jsx"
import { DataTable } from "../../components/data-table.jsx"

const eventData = [
  { event: "Product Launch", date: "2024-01-15", attendees: 450, engagement: 85, revenue: 125000 },
  { event: "Webinar Series", date: "2024-01-20", attendees: 320, engagement: 78, revenue: 45000 },
  { event: "Trade Show", date: "2024-02-05", attendees: 1200, engagement: 92, revenue: 280000 },
  { event: "Workshop", date: "2024-02-12", attendees: 180, engagement: 88, revenue: 25000 },
  { event: "Conference", date: "2024-02-25", attendees: 800, engagement: 90, revenue: 195000 },
]

const monthlyEvents = [
  { month: "Jan", events: 12, attendees: 2450, revenue: 185000 },
  { month: "Feb", events: 15, attendees: 3200, revenue: 245000 },
  { month: "Mar", events: 18, attendees: 3800, revenue: 320000 },
  { month: "Apr", events: 14, attendees: 2900, revenue: 210000 },
  { month: "May", events: 20, attendees: 4200, revenue: 380000 },
  { month: "Jun", events: 16, attendees: 3500, revenue: 295000 },
]

const eventTypes = [
  { name: "Webinars", value: 35, color: "#8884d8" },
  { name: "Workshops", value: 25, color: "#82ca9d" },
  { name: "Conferences", value: 20, color: "#ffc658" },
  { name: "Trade Shows", value: 15, color: "#ff7300" },
  { name: "Other", value: 5, color: "#00ff00" },
]

const columns = [
  {
    accessorKey: "event",
    header: "Event Name",
  },
  {
    accessorKey: "date",
    header: "Date",
  },
  {
    accessorKey: "attendees",
    header: "Attendees",
    cell: ({ row }) => <Badge variant="secondary">{row.getValue("attendees")} people</Badge>,
  },
  {
    accessorKey: "engagement",
    header: "Engagement",
    cell: ({ row }) => (
      <Badge variant={row.getValue("engagement") > 85 ? "default" : "secondary"}>{row.getValue("engagement")}%</Badge>
    ),
  },
  {
    accessorKey: "revenue",
    header: "Revenue",
    cell: ({ row }) => <span className="font-medium">${row.getValue("revenue").toLocaleString()}</span>,
  },
]

export default function EventsReportingPage() {
  const [dateRange, setDateRange] = useState("last-30-days")
  const [eventType, setEventType] = useState("all")
  const [loading, setLoading] = useState(false)

  const handleExportReport = () => {
    setLoading(true)
    // Simulate export process
    setTimeout(() => {
      setLoading(false)
      // In a real app, this would trigger a download
      alert("Report exported successfully!")
    }, 2000)
  }

  const totalAttendees = eventData.reduce((sum, event) => sum + event.attendees, 0)
  const totalRevenue = eventData.reduce((sum, event) => sum + event.revenue, 0)
  const avgEngagement = eventData.reduce((sum, event) => sum + event.engagement, 0) / eventData.length

  return (
    <div className="space-y-6">
      <PageHeader title="Events Reporting" description="Comprehensive analytics and reporting for all events" />

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Label htmlFor="date-range">Date Range</Label>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger>
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-7-days">Last 7 days</SelectItem>
              <SelectItem value="last-30-days">Last 30 days</SelectItem>
              <SelectItem value="last-90-days">Last 90 days</SelectItem>
              <SelectItem value="last-year">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex-1">
          <Label htmlFor="event-type">Event Type</Label>
          <Select value={eventType} onValueChange={setEventType}>
            <SelectTrigger>
              <SelectValue placeholder="Select event type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Events</SelectItem>
              <SelectItem value="webinars">Webinars</SelectItem>
              <SelectItem value="workshops">Workshops</SelectItem>
              <SelectItem value="conferences">Conferences</SelectItem>
              <SelectItem value="trade-shows">Trade Shows</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-end">
          <Button onClick={handleExportReport} disabled={loading}>
            <Download className="h-4 w-4 mr-2" />
            {loading ? "Exporting..." : "Export Report"}
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{eventData.length}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12% from last period
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Attendees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAttendees.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8% from last period
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Engagement</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgEngagement.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +3% from last period
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(totalRevenue / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +15% from last period
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Event Details</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="types">Event Types</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Event Performance</CardTitle>
                <CardDescription>Events, attendees, and revenue by month</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={monthlyEvents}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="attendees" fill="#8884d8" name="Attendees" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
                <CardDescription>Monthly revenue from events</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={monthlyEvents}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="revenue" stroke="#82ca9d" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Performance Details</CardTitle>
              <CardDescription>Detailed performance metrics for each event</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={eventData} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Count Trends</CardTitle>
              <CardDescription>Number of events organized per month</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={monthlyEvents}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="events" stroke="#8884d8" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="types" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Type Distribution</CardTitle>
              <CardDescription>Breakdown of events by type</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={eventTypes}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {eventTypes.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>

                <div className="space-y-4">
                  {eventTypes.map((type, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: type.color }} />
                        <span className="font-medium">{type.name}</span>
                      </div>
                      <Badge variant="outline">{type.value}%</Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
