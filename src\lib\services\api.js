// apiService.js

import { API_CONFIG, API_ENDPOINTS } from "../config/api"

/**
 * @typedef {Object} ApiResponse
 * @property {boolean} success
 * @property {any} [data]
 * @property {string} [message]
 * @property {string} [error]
 * @property {{page:number, limit:number, total:number, totalPages:number}} [pagination]
 */

/**
 * @typedef {Object} ApiRequestOptions
 * @property {"GET" | "POST" | "PUT" | "DELETE"} [method]
 * @property {Object<string, string>} [headers]
 * @property {any} [body]
 * @property {Object<string, string>} [params]
 * @property {any} [fallback]
 */

class ApiService {
  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
    this.isDevelopment = process.env.NODE_ENV === "development"
  }

  /**
   * @param {string} endpoint
   * @param {ApiRequestOptions} options
   * @returns {Promise<ApiResponse>}
   */
  async request(endpoint, options = {}) {
    const { method = "GET", headers = {}, body, params, fallback } = options

    // Build URL with query parameters
    const url = new URL(`${this.baseUrl}${endpoint}`)
    if (params && method === "GET") {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value)
      })
    }

    // Prepare request config
    const config = {
      method,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      credentials: "include",
    }

    if (body && method !== "GET") {
      config.body = JSON.stringify(body)
    }

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)

      const response = await fetch(url.toString(), {
        ...config,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          if (typeof window !== "undefined") {
            window.location.href = "/login"
          }
        }
        throw new Error(data.error || data.message || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error("API request failed:", error)

      if (this.isDevelopment && fallback) {
        console.warn(`Using fallback data for ${endpoint} due to error:`, error.message)
        return {
          success: true,
          data: fallback,
          message: "Using fallback data (API error)",
        }
      }

      throw error
    }
  }

  auth = {
    login: (email, password) =>
      this.request(API_ENDPOINTS.AUTH.LOGIN, {
        method: "POST",
        body: { email, password },
        fallback: {
          user: {
            id: "1",
            name: "Admin User",
            email: "<EMAIL>",
            role: "admin",
            department: "IT",
            isActive: true,
          },
          token: "mock-jwt-token",
        },
      }),

    logout: () =>
      this.request(API_ENDPOINTS.AUTH.LOGOUT, {
        method: "POST",
        fallback: { success: true },
      }),

    register: (userData) =>
      this.request(API_ENDPOINTS.AUTH.REGISTER, {
        method: "POST",
        body: userData,
      }),

    me: () =>
      this.request(API_ENDPOINTS.AUTH.ME, {
        fallback: {
          id: "1",
          name: "Admin User",
          email: "<EMAIL>",
          role: "admin",
          department: "IT",
          isActive: true,
        },
      }),

    refresh: () =>
      this.request(API_ENDPOINTS.AUTH.REFRESH, {
        method: "POST",
        fallback: { success: true },
      }),
  }
  customers = {
    getAll: (params = {}) =>
      this.request(API_ENDPOINTS.CUSTOMERS.GET_ALL, {
        method: "GET",
        params,
        fallback: generateMockCustomers(params), // You can replace this with your mock generator or static data
      }),

    getById: (id) =>
      this.request(`${API_ENDPOINTS.CUSTOMERS.GET_BY_ID}${id}`, {
        method: "GET",
        fallback: generateMockCustomers().find((c) => c.id === id) || null,
      }),
  }

  analytics = {
    getDashboard: () =>
      this.request(API_ENDPOINTS.ANALYTICS.DASHBOARD, {
        method: "GET",
        fallback: {
          totalRevenue: 12500000,
          totalCustomers: 2847,
          totalTransactions: 15632,
          conversionRate: 3.2,
          revenueGrowth: 12.5,
          customerGrowth: 8.3,
          transactionGrowth: 15.7,
          conversionGrowth: -2.1,
          monthlyRevenue: [
            { month: "Jan", revenue: 950000 },
            { month: "Feb", revenue: 1050000 },
            { month: "Mar", revenue: 1150000 },
            { month: "Apr", revenue: 1250000 },
            { month: "May", revenue: 1350000 },
            { month: "Jun", revenue: 1450000 },
            { month: "Jul", revenue: 1400000 },
            { month: "Aug", revenue: 1300000 },
            { month: "Sep", revenue: 1600000 },
            { month: "Oct", revenue: 1800000 },
            { month: "Nov", revenue: 2000000 },
            { month: "Dec", revenue: 1750000 },
          ],
          topChannels: [
            { name: "OA Mag", value: 35, revenue: 4375000 },
            { name: "New Media", value: 25, revenue: 3125000 },
            { name: "Events", value: 20, revenue: 2500000 },
            { name: "R4G", value: 15, revenue: 1875000 },
            { name: "M4G", value: 5, revenue: 625000 },
          ],
          topCities: [
            { city: "Mumbai", revenue: 3000000, customers: 450 },
            { city: "Delhi", revenue: 2500000, customers: 380 },
            { city: "Bangalore", revenue: 2200000, customers: 420 },
            { city: "Chennai", revenue: 1800000, customers: 290 },
            { city: "Pune", revenue: 1500000, customers: 250 },
          ],
        },
      }),
  }
  
}

const apiService = new ApiService()

export { apiService }
