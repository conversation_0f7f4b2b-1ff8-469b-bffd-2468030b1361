export interface Transaction {
  id: string
  date: string
  customerId: string
  customerName: string
  mediumId: string
  mediumName: string
  channelId: string
  channelName: string
  propertyId: string
  propertyName: string
  variantId?: string
  variantName?: string
  amount: number
  status: "pending" | "confirmed" | "cancelled" | "completed"
  paymentStatus: "unpaid" | "partial" | "paid"
  paymentDueDate?: string
  createdBy: string
  createdAt: string
  updatedAt?: string
  description?: string
  tags?: string[]
  invoiceNumber?: string
  currency: string
  notes?: string
}

export const sampleTransactions: Transaction[] = [
  {
    id: "TRX-001",
    date: "2023-10-15",
    customerId: "CUST-001",
    customerName: "Acme Displays",
    mediumId: "MED-001",
    mediumName: "Event",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    propertyId: "PROP-001",
    propertyName: "Exhibitor",
    variantId: "VAR-001",
    variantName: "Premium Booth",
    amount: 250000,
    status: "completed",
    paymentStatus: "paid",
    paymentDueDate: "2023-10-30",
    createdBy: "user-001",
    createdAt: "2023-09-15T10:30:00Z",
    updatedAt: "2023-10-15T14:20:00Z",
    description: "Premium booth space at DDX Asia 2023",
    tags: ["DDX", "2023", "Exhibitor"],
    invoiceNumber: "INV-20231015-001",
    currency: "INR",
  },
  {
    id: "TRX-002",
    date: "2023-10-18",
    customerId: "CUST-002",
    customerName: "TechVision Inc",
    mediumId: "MED-001",
    mediumName: "Event",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    propertyId: "PROP-005",
    propertyName: "Sponsor",
    variantId: "VAR-010",
    variantName: "Title Sponsor",
    amount: 1500000,
    status: "confirmed",
    paymentStatus: "partial",
    paymentDueDate: "2023-11-15",
    createdBy: "user-002",
    createdAt: "2023-10-01T09:15:00Z",
    description: "Title sponsorship for DDX Asia 2023",
    tags: ["DDX", "2023", "Sponsor", "Title"],
    invoiceNumber: "INV-20231018-002",
    currency: "INR",
  },
  {
    id: "TRX-003",
    date: "2023-10-20",
    customerId: "CUST-003",
    customerName: "MediaWorks",
    mediumId: "MED-002",
    mediumName: "Print",
    channelId: "CHA-005",
    channelName: "OA Magazine",
    propertyId: "PROP-010",
    propertyName: "Advertisement",
    variantId: "VAR-025",
    variantName: "Full Page Color",
    amount: 75000,
    status: "confirmed",
    paymentStatus: "unpaid",
    paymentDueDate: "2023-11-20",
    createdBy: "user-003",
    createdAt: "2023-10-10T11:45:00Z",
    description: "Full page color advertisement in Nov 2023 issue",
    tags: ["OA", "Print", "Full Page"],
    invoiceNumber: "INV-20231020-003",
    currency: "INR",
  },
  {
    id: "TRX-004",
    date: "2023-10-22",
    customerId: "CUST-004",
    customerName: "SignagePro",
    mediumId: "MED-003",
    mediumName: "Web",
    channelId: "CHA-010",
    channelName: "R4G Website",
    propertyId: "PROP-015",
    propertyName: "Banner Ad",
    variantId: "VAR-040",
    variantName: "Homepage Premium",
    amount: 45000,
    status: "completed",
    paymentStatus: "paid",
    createdBy: "user-004",
    createdAt: "2023-10-15T13:20:00Z",
    description: "Premium banner ad on R4G website homepage for 3 months",
    tags: ["Web", "Banner", "Homepage"],
    invoiceNumber: "INV-20231022-004",
    currency: "INR",
  },
  {
    id: "TRX-005",
    date: "2023-10-25",
    customerId: "CUST-005",
    customerName: "DigitalAds Co",
    mediumId: "MED-001",
    mediumName: "Event",
    channelId: "CHA-002",
    channelName: "OAC",
    propertyId: "PROP-002",
    propertyName: "Award Entry",
    variantId: "VAR-015",
    variantName: "Multiple Categories",
    amount: 35000,
    status: "confirmed",
    paymentStatus: "paid",
    createdBy: "user-005",
    createdAt: "2023-10-20T15:30:00Z",
    description: "Entry to multiple categories in OAC Awards 2023",
    tags: ["OAC", "Awards", "2023"],
    invoiceNumber: "INV-20231025-005",
    currency: "INR",
  },
]
