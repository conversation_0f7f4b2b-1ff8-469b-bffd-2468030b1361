// API Configuration for React.js
export const API_CONFIG = {
  BASE_URL: "http://localhost:4000/api",
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Health check
  HEALTH: "/health",

  // Auth
  AUTH: {
    LOGIN: "/users/auth/login",
    LOGOUT: "/auth/logout",
    REGISTER: "/auth/register",
    REFRESH: "/auth/refresh",
    ME: "/auth/me",
  },

  // Users
  USERS: {
    ALL: "/users",
    CREATE: "/users",
    UPDATE: "/users",
    DELETE: "/users",
    BY_ID: "/users",
  },

  // Customers
  CUSTOMERS: {
    ALL: "/customers",
    CREATE: "/customers",
    UPDATE: "/customers",
    DELETE: "/customers",
    BY_ID: "/customers",
    SEARCH: "/customers/search",
  },

  // Transactions
  TRANSACTIONS: {
    ALL: "/transactions",
    CREATE: "/transactions",
    UPDATE: "/transactions",
    DELETE: "/transactions",
    BY_ID: "/transactions",
    BY_CUSTOMER: "/transactions/by-customer",
  },

  // Masters
  MASTERS: {
    MEDIUMS: {
      ALL: "/masters/mediums",
      CREATE: "/masters/mediums",
      UPDATE: "/masters/mediums",
      DELETE: "/masters/mediums",
    },
    CHANNELS: {
      ALL: "/masters/channels",
      CREATE: "/masters/channels",
      UPDATE: "/masters/channels",
      DELETE: "/masters/channels",
    },
    PROPERTIES: {
      ALL: "/masters/properties",
      CREATE: "/masters/properties",
      UPDATE: "/masters/properties",
      DELETE: "/masters/properties",
    },
    VARIANTS: {
      ALL: "/masters/variants",
      CREATE: "/masters/variants",
      UPDATE: "/masters/variants",
      DELETE: "/masters/variants",
    },
    CITIES: {
      ALL: "/masters/cities",
      CREATE: "/masters/cities",
      UPDATE: "/masters/cities",
      DELETE: "/masters/cities",
    },
    STAGES: {
      ALL: "/masters/stages",
      CREATE: "/masters/stages",
      UPDATE: "/masters/stages",
      DELETE: "/masters/stages",
    },
    CONTACTS: {
      ALL: "/masters/contacts",
      CREATE: "/masters/contacts",
      UPDATE: "/masters/contacts",
      DELETE: "/masters/contacts",
    },
    COMPANIES: {
      ALL: "/masters/companies",
      CREATE: "/masters/companies",
      UPDATE: "/masters/companies",
      DELETE: "/masters/companies",
    },
    COMPANY_CATEGORIES: {
      ALL: "/masters/company-categories",
      CREATE: "/masters/company-categories",
      UPDATE: "/masters/company-categories",
      DELETE: "/masters/company-categories",
    },
  },

  // Analytics
  ANALYTICS: {
    DASHBOARD: "/analytics/dashboard",
    REVENUE: "/analytics/revenue",
    CUSTOMER_JOURNEY: "/analytics/customer-journey",
    DEMOGRAPHICS: "/analytics/demographics",
    EVENTS: "/analytics/events",
  },

  // Reports
  REPORTS: {
    GENERATE: "/reports/generate",
    DOWNLOAD: "/reports/download",
    LIST: "/reports/list",
  },

  // Customer Journey
  CUSTOMER_JOURNEY: {
    ALL: "/customer-journey",
    CREATE: "/customer-journey",
    UPDATE: "/customer-journey",
    DELETE: "/customer-journey",
    BULK_UPLOAD: "/customer-journey/bulk-upload",
  },
};
