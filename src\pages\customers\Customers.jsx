"use client";

import { useState, useEffect } from "react";
import { Plus, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { Button } from "../../components/ui/button.jsx";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card.jsx";
import { Badge } from "../../components/ui/badge.jsx";
import { DataTable } from "../../components/data-table.jsx";
import { CustomerForm } from "../../components/forms/customer-form.jsx";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog.jsx";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu.jsx";
import { useToast } from "@/hooks/use-toast";
import customerService from "../../services/customer.service.js";
import cityService from "../../services/cities.service.js";

export default function CustomersPage() {
  const { toast } = useToast();
  const [customers, setCustomers] = useState([]);
  const [cities, setCities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // Fetch customers from API using the service
  const fetchCustomers = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await customerService.getAllCustomers({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const customersArray = Array.isArray(result.customers)
        ? result.customers
        : [];
      setCustomers(customersArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch companies",
        variant: "destructive",
      });
      console.error("Error fetching companies:", error);
      setCustomers([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch cities from API
  const fetchCities = async () => {
    try {
      const result = await cityService.getAllCities();
      const citiesArray = Array.isArray(result.cities) ? result.cities : [];
      setCities(citiesArray);
    } catch (error) {
      console.error("Error fetching cities:", error);
      setCities([]);
    }
  };

  useEffect(() => {
    fetchCustomers();
    fetchCities();
  }, []);

  const handleAddCustomer = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditCustomer = (customer) => {
    setSelectedCustomer(customer);
    setIsEditDialogOpen(true);
  };

  const handleDeleteCustomer = (customer) => {
    setSelectedCustomer(customer);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateCustomer = async (data) => {
    try {
      await customerService.createCustomer(data);
      await fetchCustomers();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Company created successfully",
      });
    } catch (error) {
      console.error("Create company error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create company",
        variant: "destructive",
      });
    }
  };

  const handleUpdateCustomer = async (data) => {
    try {
      await customerService.updateCustomer(selectedCustomer.id, data);
      await fetchCustomers();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Company updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update company",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteCustomer = async () => {
    try {
      await customerService.deleteCustomer(selectedCustomer.id);
      await fetchCustomers();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Company deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete company",
        variant: "destructive",
      });
    }
  };

  const handleImportCustomers = async (file) => {
    try {
      const result = await customerService.bulkImportCustomers(file);

      await fetchCustomers(); // Refresh the customers list
      toast({
        title: "Success",
        description: "Companies imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import companies from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Company Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "address",
      header: "Address",
    },
    {
      accessorKey: "cityId",
      header: "City",
      cell: ({ row }) => {
        const cityId = row.getValue("cityId");
        const city = cities.find((c) => c.id.toString() === cityId?.toString());
        return <div>{city ? city.name : "N/A"}</div>;
      },
    },
    {
      accessorKey: "state",
      header: "State",
    },
    {
      accessorKey: "country",
      header: "Country",
    },
    {
      accessorKey: "pincode",
      header: "Pincode",
    },
    {
      accessorKey: "website",
      header: "Website",
    },
    {
      accessorKey: "industry",
      header: "Industry",
    },
    {
      accessorKey: "companyCategory",
      header: "Company Category",
    },
    {
      accessorKey: "gstNumber",
      header: "GST Number",
    },
    {
      accessorKey: "panNumber",
      header: "PAN Number",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        const getStatusColor = (status) => {
          switch (status) {
            case "active":
              return "success";
            case "inactive":
              return "secondary";
            case "lead":
              return "outline";
            default:
              return "secondary";
          }
        };
        return <Badge variant={getStatusColor(status)}>{status}</Badge>;
      },
    },
    {
      accessorKey: "notes",
      header: "Notes",
      cell: ({ row }) => {
        const notes = row.getValue("notes");
        return <div className="max-w-xs truncate">{notes || "N/A"}</div>;
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const customer = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditCustomer(customer)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteCustomer(customer)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Companies Management</CardTitle>
              <CardDescription>
                Manage your companies and their information
              </CardDescription>
            </div>
            <Button onClick={handleAddCustomer}>
              <Plus className="mr-2 h-4 w-4" />
              Add Company
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={customers}
            searchKey="name"
            isLoading={isLoading}
            entityName="Companies"
            onImport={handleImportCustomers}
            templatePath="/templates/customers_template.xlsx"
            acceptedFileTypes=".xlsx,.xls"
            fileDescription="File should contain columns: name, email, phone, address, cityId, state, country, pincode, website, industry, companyCategory, gstNumber, panNumber, status, notes"
          />
        </CardContent>
      </Card>

      {/* Add Customer Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Add New Company</DialogTitle>
            <DialogDescription>
              Create a new company record with their information.
            </DialogDescription>
          </DialogHeader>
          <CustomerForm
            onSubmit={handleCreateCustomer}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Customer Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Edit Company</DialogTitle>
            <DialogDescription>
              Update the company information.
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <CustomerForm
              defaultValues={selectedCustomer}
              onSubmit={handleUpdateCustomer}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Customer Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedCustomer?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCustomer}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
