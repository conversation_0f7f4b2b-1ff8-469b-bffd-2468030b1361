export interface User {
  id: string
  email: string
  name: string
  role: "admin" | "manager" | "user" | "viewer"
  department?: string
  phone?: string
  avatar?: string
  isActive: boolean
  permissions: string[]
  createdAt: string
  updatedAt?: string
  lastLogin?: string
}

export const sampleUsers: User[] = [
  {
    id: "USER-001",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    department: "Management",
    phone: "+91 98765 43210",
    isActive: true,
    permissions: ["all"],
    createdAt: "2023-01-01T00:00:00Z",
    lastLogin: "2023-10-25T09:30:00Z",
  },
  {
    id: "USER-002",
    email: "<EMAIL>",
    name: "Sales Manager",
    role: "manager",
    department: "Sales",
    phone: "+91 98765 43211",
    isActive: true,
    permissions: ["customers.read", "customers.write", "transactions.read", "transactions.write"],
    createdAt: "2023-01-15T00:00:00Z",
    lastLogin: "2023-10-24T14:20:00Z",
  },
  {
    id: "USER-003",
    email: "<EMAIL>",
    name: "Sales Executive",
    role: "user",
    department: "Sales",
    phone: "+91 98765 43212",
    isActive: true,
    permissions: ["customers.read", "transactions.read"],
    createdAt: "2023-02-01T00:00:00Z",
    lastLogin: "2023-10-25T08:15:00Z",
  },
]
