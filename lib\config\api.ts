// API Configuration
export const API_CONFIG = {
  BASE_URL: "http://localhost:4000/api",
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Health check
  HEALTH: "/health",

  // Auth
  AUTH: {
    LOGIN: "/auth/login",
    LOGOUT: "/auth/logout",
    REGISTER: "/auth/register",
    REFRESH: "/auth/refresh",
    ME: "/auth/me",
  },

  // Users
  USERS: {
    ALL: "/users/all",
    CREATE: "/users/create",
    UPDATE: "/users/update",
    DELETE: "/users/delete",
    BY_ID: "/users/by-id",
  },

  // Customers
  CUSTOMERS: {
    ALL: "/customers/all",
    CREATE: "/customers/create",
    UPDATE: "/customers/update",
    DELETE: "/customers/delete",
    BY_ID: "/customers/by-id",
    SEARCH: "/customers/search",
  },

  // Transactions
  TRANSACTIONS: {
    ALL: "/transactions/all",
    CREATE: "/transactions/create",
    UPDATE: "/transactions/update",
    DELETE: "/transactions/delete",
    BY_ID: "/transactions/by-id",
    BY_CUSTOMER: "/transactions/by-customer",
  },

  // Masters
  MASTERS: {
    MEDIUMS: {
      ALL: "/masters/medium/all",
      CREATE: "/masters/medium/create",
      UPDATE: "/masters/medium/update",
      DELETE: "/masters/medium/delete",
    },
    CHANNELS: {
      ALL: "/masters/channels/all",
      CREATE: "/masters/channels/create",
      UPDATE: "/masters/channels/update",
      DELETE: "/masters/channels/delete",
    },
    PROPERTIES: {
      ALL: "/masters/properties/all",
      CREATE: "/masters/properties/create",
      UPDATE: "/masters/properties/update",
      DELETE: "/masters/properties/delete",
    },
    VARIANTS: {
      ALL: "/masters/variants/all",
      CREATE: "/masters/variants/create",
      UPDATE: "/masters/variants/update",
      DELETE: "/masters/variants/delete",
    },
    CITIES: {
      ALL: "/masters/cities/all",
      CREATE: "/masters/cities/create",
      UPDATE: "/masters/cities/update",
      DELETE: "/masters/cities/delete",
    },
  },

  // Analytics
  ANALYTICS: {
    DASHBOARD: "/analytics/dashboard",
    REVENUE: "/analytics/revenue",
    CUSTOMER_JOURNEY: "/analytics/customer-journey",
    DEMOGRAPHICS: "/analytics/demographics",
    EVENTS: "/analytics/events",
  },

  // Reports
  REPORTS: {
    GENERATE: "/reports/generate",
    DOWNLOAD: "/reports/download",
    LIST: "/reports/list",
  },

  // Customer Journey
  CUSTOMER_JOURNEY: {
    ALL: "/customer-journey/all",
    CREATE: "/customer-journey/create",
    UPDATE: "/customer-journey/update",
    DELETE: "/customer-journey/delete",
    BULK_UPLOAD: "/customer-journey/bulk-upload",
  },
};
