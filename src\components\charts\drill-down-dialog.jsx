import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { BarChart } from "./bar-chart"

export function DrillDownDialog({ open, onOpenChange, title, data, categories, index }) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>Detailed breakdown of the selected data</DialogDescription>
        </DialogHeader>
        <div className="h-[400px] mt-4">
          <BarChart data={data} categories={categories} index={index} className="h-full" />
        </div>
      </DialogContent>
    </Dialog>
  )
}
