"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Plus, MapPin, TrendingUp, Users, Target } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Link } from "react-router-dom"

const journeyData = [
  {
    id: "CJ-001",
    customer: "Acme Corp",
    stage: "Awareness",
    touchpoint: "Social Media",
    source: "LinkedIn",
    date: "2024-01-15",
    status: "Active",
    value: 5000,
  },
  {
    id: "CJ-002",
    customer: "Tech Solutions",
    stage: "Consideration",
    touchpoint: "Website",
    source: "Organic Search",
    date: "2024-01-18",
    status: "Active",
    value: 8500,
  },
  {
    id: "CJ-003",
    customer: "Global Industries",
    stage: "Conversion",
    touchpoint: "Sales Call",
    source: "Direct",
    date: "2024-01-20",
    status: "Completed",
    value: 25000,
  },
  {
    id: "CJ-004",
    customer: "StartupXYZ",
    stage: "Retention",
    touchpoint: "Email",
    source: "Newsletter",
    date: "2024-01-22",
    status: "Active",
    value: 3200,
  },
]

const stageMetrics = [
  { stage: "Awareness", count: 45, conversion: 68 },
  { stage: "Interest", count: 32, conversion: 75 },
  { stage: "Consideration", count: 24, conversion: 82 },
  { stage: "Intent", count: 18, conversion: 89 },
  { stage: "Conversion", count: 16, conversion: 94 },
  { stage: "Retention", count: 12, conversion: 85 },
]

const touchpointData = [
  { touchpoint: "Website", interactions: 156, conversions: 23 },
  { touchpoint: "Email", interactions: 89, conversions: 15 },
  { touchpoint: "Social Media", interactions: 134, conversions: 18 },
  { touchpoint: "Sales Call", interactions: 45, conversions: 32 },
  { touchpoint: "Webinar", interactions: 67, conversions: 12 },
]

export default function CustomerJourneyPage() {
  const [selectedStage, setSelectedStage] = useState("all")

  const filteredJourneys =
    selectedStage === "all"
      ? journeyData
      : journeyData.filter((journey) => journey.stage.toLowerCase() === selectedStage)

  const totalJourneys = journeyData.length
  const activeJourneys = journeyData.filter((j) => j.status === "Active").length
  const completedJourneys = journeyData.filter((j) => j.status === "Completed").length
  const totalValue = journeyData.reduce((sum, j) => sum + j.value, 0)

  const getStageColor = (stage) => {
    const colors = {
      Awareness: "bg-blue-100 text-blue-800",
      Interest: "bg-purple-100 text-purple-800",
      Consideration: "bg-yellow-100 text-yellow-800",
      Intent: "bg-orange-100 text-orange-800",
      Conversion: "bg-green-100 text-green-800",
      Retention: "bg-indigo-100 text-indigo-800",
    }
    return colors[stage] || "bg-gray-100 text-gray-800"
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800"
      case "Completed":
        return "bg-blue-100 text-blue-800"
      case "Paused":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Customer Journey</h1>
          <p className="text-muted-foreground">Track and analyze customer interactions across all touchpoints</p>
        </div>
        <Link to="/customer-journey/add">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Journey
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Journeys</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalJourneys}</div>
            <p className="text-xs text-muted-foreground">Tracked customer journeys</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Journeys</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeJourneys}</div>
            <p className="text-xs text-muted-foreground">
              {((activeJourneys / totalJourneys) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedJourneys}</div>
            <p className="text-xs text-muted-foreground">Successful conversions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(totalValue / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">Pipeline value</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="journeys" className="space-y-6">
        <TabsList>
          <TabsTrigger value="journeys">All Journeys</TabsTrigger>
          <TabsTrigger value="stages">Stage Analysis</TabsTrigger>
          <TabsTrigger value="touchpoints">Touchpoints</TabsTrigger>
        </TabsList>

        <TabsContent value="journeys" className="space-y-6">
          {/* Stage Filters */}
          <div className="flex space-x-2">
            <Button variant={selectedStage === "all" ? "default" : "outline"} onClick={() => setSelectedStage("all")}>
              All Stages
            </Button>
            {["awareness", "consideration", "conversion", "retention"].map((stage) => (
              <Button
                key={stage}
                variant={selectedStage === stage ? "default" : "outline"}
                onClick={() => setSelectedStage(stage)}
              >
                {stage.charAt(0).toUpperCase() + stage.slice(1)}
              </Button>
            ))}
          </div>

          {/* Journey Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredJourneys.map((journey) => (
              <Card key={journey.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{journey.customer}</CardTitle>
                    <Badge className={getStatusColor(journey.status)}>{journey.status}</Badge>
                  </div>
                  <CardDescription>{journey.id}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Stage:</span>
                      <Badge className={getStageColor(journey.stage)}>{journey.stage}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Touchpoint:</span>
                      <span className="text-sm font-medium">{journey.touchpoint}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Source:</span>
                      <span className="text-sm">{journey.source}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Value:</span>
                      <span className="text-sm font-medium">${journey.value.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Date:</span>
                      <span className="text-sm">{journey.date}</span>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <Button variant="outline" size="sm" className="w-full">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="stages" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Journey Stage Analysis</CardTitle>
              <CardDescription>Customer progression through journey stages</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={stageMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="stage" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" name="Customers" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="touchpoints" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Touchpoint Performance</CardTitle>
              <CardDescription>Interactions and conversions by touchpoint</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={touchpointData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="touchpoint" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="interactions" fill="#8884d8" name="Interactions" />
                  <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
