"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card.jsx";
import { But<PERSON> } from "../../components/ui/button.jsx";
import { Input } from "../../components/ui/input.jsx";
import { Label } from "../../components/ui/label.jsx";
import { Checkbox } from "../../components/ui/checkbox.jsx";
import { Badge } from "../../components/ui/badge.jsx";
import {
  Tabs,
  Tabs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "../../components/ui/tabs.jsx";
import { Target, Users, Filter, Save } from "lucide-react";
import { PageHeader } from "../../components/page-header.jsx";
import { DataTable } from "../../components/data-table.jsx";

const targetingCriteria = [
  {
    id: 1,
    name: "Age Range",
    type: "demographic",
    options: ["18-25", "26-35", "36-45", "46-55", "55+"],
  },
  {
    id: 2,
    name: "Gender",
    type: "demographic",
    options: ["Male", "Female", "Other"],
  },
  {
    id: 3,
    name: "Location",
    type: "geographic",
    options: ["Urban", "Suburban", "Rural"],
  },
  {
    id: 4,
    name: "Income Level",
    type: "demographic",
    options: ["<$30k", "$30k-$50k", "$50k-$75k", "$75k-$100k", "$100k+"],
  },
  {
    id: 5,
    name: "Interests",
    type: "behavioral",
    options: ["Technology", "Sports", "Travel", "Fashion", "Food"],
  },
];

const savedAudiences = [
  {
    id: 1,
    name: "Premium Tech Enthusiasts",
    description: "High-income tech lovers aged 25-45",
    size: "12,450",
    criteria: ["Age: 25-45", "Income: $75k+", "Interest: Technology"],
    status: "Active",
    created: "2024-01-15",
  },
  {
    id: 2,
    name: "Young Urban Professionals",
    description: "Urban millennials with disposable income",
    size: "8,230",
    criteria: ["Age: 26-35", "Location: Urban", "Income: $50k+"],
    status: "Active",
    created: "2024-01-10",
  },
  {
    id: 3,
    name: "Family Shoppers",
    description: "Parents looking for family products",
    size: "15,670",
    criteria: ["Age: 30-50", "Interest: Family", "Location: Suburban"],
    status: "Paused",
    created: "2024-01-05",
  },
];

const columns = [
  {
    accessorKey: "name",
    header: "Audience Name",
  },
  {
    accessorKey: "size",
    header: "Audience Size",
    cell: ({ row }) => (
      <Badge variant="secondary">{row.getValue("size")} users</Badge>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <Badge
        variant={row.getValue("status") === "Active" ? "default" : "secondary"}
      >
        {row.getValue("status")}
      </Badge>
    ),
  },
  {
    accessorKey: "created",
    header: "Created",
  },
];

export default function TargetingPage() {
  const [selectedCriteria, setSelectedCriteria] = useState({});
  const [audienceName, setAudienceName] = useState("");
  const [audienceDescription, setAudienceDescription] = useState("");
  const [estimatedSize, setEstimatedSize] = useState(0);

  useEffect(() => {
    // Calculate estimated audience size based on selected criteria
    const criteriaCount = Object.keys(selectedCriteria).length;
    const baseSize = 50000;
    const reduction = criteriaCount * 0.3;
    setEstimatedSize(Math.max(1000, Math.floor(baseSize * (1 - reduction))));
  }, [selectedCriteria]);

  const handleCriteriaChange = (criteriaId, value, checked) => {
    setSelectedCriteria((prev) => {
      const newCriteria = { ...prev };
      if (!newCriteria[criteriaId]) {
        newCriteria[criteriaId] = [];
      }

      if (checked) {
        newCriteria[criteriaId] = [...newCriteria[criteriaId], value];
      } else {
        newCriteria[criteriaId] = newCriteria[criteriaId].filter(
          (v) => v !== value
        );
        if (newCriteria[criteriaId].length === 0) {
          delete newCriteria[criteriaId];
        }
      }

      return newCriteria;
    });
  };

  const handleSaveAudience = () => {
    if (!audienceName.trim()) {
      alert("Please enter an audience name");
      return;
    }

    // Reset form
    setAudienceName("");
    setAudienceDescription("");
    setSelectedCriteria({});
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Audience Targeting"
        description="Create and manage targeted audience segments for campaigns"
      />

      <Tabs defaultValue="create" className="space-y-4">
        <TabsList>
          <TabsTrigger value="create">Create Audience</TabsTrigger>
          <TabsTrigger value="manage">Manage Audiences</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Filter className="h-5 w-5" />
                  <span>Targeting Criteria</span>
                </CardTitle>
                <CardDescription>
                  Select criteria to define your target audience
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {targetingCriteria.map((criteria) => (
                  <div key={criteria.id} className="space-y-3">
                    <Label className="text-sm font-medium">
                      {criteria.name}
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      {criteria.options.map((option) => (
                        <div
                          key={option}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`${criteria.id}-${option}`}
                            checked={
                              selectedCriteria[criteria.id]?.includes(option) ||
                              false
                            }
                            onCheckedChange={(checked) =>
                              handleCriteriaChange(criteria.id, option, checked)
                            }
                          />
                          <Label
                            htmlFor={`${criteria.id}-${option}`}
                            className="text-sm font-normal"
                          >
                            {option}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>Audience Details</span>
                </CardTitle>
                <CardDescription>
                  Configure your audience settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="audience-name">Audience Name</Label>
                  <Input
                    id="audience-name"
                    placeholder="Enter audience name"
                    value={audienceName}
                    onChange={(e) => setAudienceName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="audience-description">Description</Label>
                  <Input
                    id="audience-description"
                    placeholder="Enter audience description"
                    value={audienceDescription}
                    onChange={(e) => setAudienceDescription(e.target.value)}
                  />
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="h-5 w-5 text-muted-foreground" />
                      <span className="font-medium">
                        Estimated Audience Size
                      </span>
                    </div>
                    <Badge variant="outline" className="text-lg">
                      {estimatedSize.toLocaleString()} users
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Selected Criteria</Label>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(selectedCriteria).map(
                      ([criteriaId, values]) => {
                        const criteria = targetingCriteria.find(
                          (c) => c.id.toString() === criteriaId
                        );
                        return values.map((value) => (
                          <Badge
                            key={`${criteriaId}-${value}`}
                            variant="secondary"
                          >
                            {criteria?.name}: {value}
                          </Badge>
                        ));
                      }
                    )}
                    {Object.keys(selectedCriteria).length === 0 && (
                      <span className="text-sm text-muted-foreground">
                        No criteria selected
                      </span>
                    )}
                  </div>
                </div>

                <Button onClick={handleSaveAudience} className="w-full">
                  <Save className="h-4 w-4 mr-2" />
                  Save Audience
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="manage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Saved Audiences</CardTitle>
              <CardDescription>
                Manage your existing audience segments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={savedAudiences} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Audiences
                </CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {savedAudiences.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Active audience segments
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Reach
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">36,350</div>
                <p className="text-xs text-muted-foreground">
                  Combined audience size
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg. Engagement
                </CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.2%</div>
                <p className="text-xs text-muted-foreground">
                  Across all audiences
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
