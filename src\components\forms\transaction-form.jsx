"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import transactionService from "../../services/transaction.service.js";
import { transactionStages } from "../../lib/models/transaction.js";

export function TransactionForm({ defaultValues, onSubmit, onCancel }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [mediums, setMediums] = useState([]);
  const [channels, setChannels] = useState([]);
  const [properties, setProperties] = useState([]);
  const [variants, setVariants] = useState([]);
  const [cities, setCities] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      customerId: defaultValues?.customerId?.toString() || "",
      mediumId: defaultValues?.mediumId?.toString() || "",
      channelId: defaultValues?.channelId?.toString() || "",
      propertyId: defaultValues?.propertyId?.toString() || "",
      variantId: defaultValues?.variantId?.toString() || "",
      cityId: defaultValues?.cityId?.toString() || "",
      stage: defaultValues?.stage || "",
      quantity: defaultValues?.quantity?.toString() || "",
      month: defaultValues?.month || "",
      year: defaultValues?.year?.toString() || "",
      revenue: defaultValues?.revenue?.toString() || "",
      notes: defaultValues?.notes || "",
    },
  });

  // Load dropdown data
  useEffect(() => {
    const loadDropdownData = async () => {
      try {
        const [
          customersData,
          mediumsData,
          channelsData,
          propertiesData,
          variantsData,
          citiesData,
        ] = await Promise.all([
          transactionService.getCustomers(),
          transactionService.getMediums(),
          transactionService.getChannels(),
          transactionService.getProperties(),
          transactionService.getVariants(),
          transactionService.getCities(),
        ]);

        setCustomers(customersData);
        setMediums(mediumsData);
        setChannels(channelsData);
        setProperties(propertiesData);
        setVariants(variantsData);
        setCities(citiesData);
      } catch (error) {
        console.error("Error loading dropdown data:", error);
      }
    };

    loadDropdownData();
  }, []);

  const handleFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-h-[70vh] overflow-y-auto pr-2">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Customer */}
          <div className="space-y-2">
            <Label htmlFor="customerId">Company *</Label>
            <Select
              value={watch("customerId")}
              onValueChange={(value) => setValue("customerId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select company" />
              </SelectTrigger>
              <SelectContent>
                {customers.map((customer) => (
                  <SelectItem key={customer.id} value={customer.id.toString()}>
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.customerId && (
              <p className="text-sm text-red-500">
                {errors.customerId.message}
              </p>
            )}
          </div>

          {/* Medium */}
          <div className="space-y-2">
            <Label htmlFor="mediumId">Medium *</Label>
            <Select
              value={watch("mediumId")}
              onValueChange={(value) => setValue("mediumId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select medium" />
              </SelectTrigger>
              <SelectContent>
                {mediums.map((medium) => (
                  <SelectItem key={medium.id} value={medium.id.toString()}>
                    {medium.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.mediumId && (
              <p className="text-sm text-red-500">{errors.mediumId.message}</p>
            )}
          </div>

          {/* Channel */}
          <div className="space-y-2">
            <Label htmlFor="channelId">Channel *</Label>
            <Select
              value={watch("channelId")}
              onValueChange={(value) => setValue("channelId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select channel" />
              </SelectTrigger>
              <SelectContent>
                {channels.map((channel) => (
                  <SelectItem key={channel.id} value={channel.id.toString()}>
                    {channel.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.channelId && (
              <p className="text-sm text-red-500">{errors.channelId.message}</p>
            )}
          </div>

          {/* Property */}
          <div className="space-y-2">
            <Label htmlFor="propertyId">Property *</Label>
            <Select
              value={watch("propertyId")}
              onValueChange={(value) => setValue("propertyId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select property" />
              </SelectTrigger>
              <SelectContent>
                {properties.map((property) => (
                  <SelectItem key={property.id} value={property.id.toString()}>
                    {property.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.propertyId && (
              <p className="text-sm text-red-500">
                {errors.propertyId.message}
              </p>
            )}
          </div>

          {/* Variant */}
          <div className="space-y-2">
            <Label htmlFor="variantId">Variant</Label>
            <Select
              value={watch("variantId")}
              onValueChange={(value) => setValue("variantId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select variant (optional)" />
              </SelectTrigger>
              <SelectContent>
                {variants.map((variant) => (
                  <SelectItem key={variant.id} value={variant.id.toString()}>
                    {variant.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* City */}
          <div className="space-y-2">
            <Label htmlFor="cityId">City</Label>
            <Select
              value={watch("cityId")}
              onValueChange={(value) => setValue("cityId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select city (optional)" />
              </SelectTrigger>
              <SelectContent>
                {cities.map((city) => (
                  <SelectItem key={city.id} value={city.id.toString()}>
                    {city.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Stage */}
          <div className="space-y-2">
            <Label htmlFor="stage">Stage</Label>
            <Select
              value={watch("stage")}
              onValueChange={(value) => setValue("stage", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select stage" />
              </SelectTrigger>
              <SelectContent>
                {transactionStages.map((stage) => (
                  <SelectItem key={stage} value={stage}>
                    {stage}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              placeholder="Enter quantity"
              {...register("quantity")}
            />
          </div>

          {/* Month */}
          <div className="space-y-2">
            <Label htmlFor="month">Month</Label>
            <Select
              value={watch("month")}
              onValueChange={(value) => setValue("month", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select month" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="January">January</SelectItem>
                <SelectItem value="February">February</SelectItem>
                <SelectItem value="March">March</SelectItem>
                <SelectItem value="April">April</SelectItem>
                <SelectItem value="May">May</SelectItem>
                <SelectItem value="June">June</SelectItem>
                <SelectItem value="July">July</SelectItem>
                <SelectItem value="August">August</SelectItem>
                <SelectItem value="September">September</SelectItem>
                <SelectItem value="October">October</SelectItem>
                <SelectItem value="November">November</SelectItem>
                <SelectItem value="December">December</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Year */}
          <div className="space-y-2">
            <Label htmlFor="year">Year</Label>
            <Input
              id="year"
              type="number"
              placeholder="Enter year"
              {...register("year")}
            />
          </div>

          {/* Revenue */}
          <div className="space-y-2">
            <Label htmlFor="revenue">Revenue</Label>
            <Input
              id="revenue"
              type="number"
              step="0.01"
              placeholder="0.00"
              {...register("revenue")}
            />
          </div>
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            placeholder="Enter notes"
            rows={3}
            {...register("notes")}
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Saving..."
              : defaultValues
              ? "Update Transaction"
              : "Save Transaction"}
          </Button>
        </div>
      </form>
    </div>
  );
}
