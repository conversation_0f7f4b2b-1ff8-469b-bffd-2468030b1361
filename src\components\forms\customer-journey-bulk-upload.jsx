"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, Download, FileText } from "lucide-react";

export function CustomerJourneyBulkUpload({ onSubmit, onCancel }) {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && selectedFile.type === "text/csv") {
      setFile(selectedFile);
    } else {
      alert("Please select a valid CSV file");
    }
  };

  const handleUpload = async () => {
    if (!file) {
      alert("Please select a file first");
      return;
    }

    setUploading(true);
    try {
      // Simulate file processing
      await new Promise((resolve) => setTimeout(resolve, 2000));
      onSubmit({ file });
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setUploading(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = `Customer Name,Email,Phone,Company,Stage,Source,First Contact Date,Notes
John Doe,<EMAIL>,+1234567890,Acme Corp,lead,website,2023-01-15,Initial inquiry
Jane Smith,<EMAIL>,+1234567891,Tech Inc,prospect,email,2023-01-20,Follow-up needed`;

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "customer_journey_template.xlsx";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <Alert>
        <FileText className="h-4 w-4" />
        <AlertDescription>
          Upload a CSV file with customer journey data. Make sure your file
          follows the required format.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>CSV Template</Label>
          <Button variant="outline" size="sm" onClick={downloadTemplate}>
            <Download className="mr-2 h-4 w-4" />
            Download Template
          </Button>
        </div>

        <div className="space-y-2">
          <Label htmlFor="file">Select CSV File</Label>
          <Input
            id="file"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
          />
          {file && (
            <p className="text-sm text-green-600">Selected: {file.name}</p>
          )}
        </div>

        <div className="bg-muted p-4 rounded-md">
          <h4 className="font-medium mb-2">Required Columns:</h4>
          <ul className="text-sm space-y-1">
            <li>• Customer Name (required)</li>
            <li>• Email (required)</li>
            <li>• Phone</li>
            <li>• Company</li>
            <li>• Stage (lead, prospect, customer, advocate)</li>
            <li>• Source (website, social, email, referral, event, other)</li>
            <li>• First Contact Date (YYYY-MM-DD format)</li>
            <li>• Notes</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleUpload} disabled={!file || uploading}>
          <Upload className="mr-2 h-4 w-4" />
          {uploading ? "Uploading..." : "Upload File"}
        </Button>
      </div>
    </div>
  );
}
