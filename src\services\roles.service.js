import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class RoleService {
  async getAllRoles(
    params = {
      page: 1,
      limit: 100,
      search: "",
      status: "",
    }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/roles?${queryString}`
        : `${API_URL}/roles`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const rolesData = Array.isArray(response.data) ? response.data : [];

      return {
        roles: rolesData,
      };
    } catch (error) {
      console.error("Error fetching roles:", error);
      throw error;
    }
  }

  async getRoleById(id) {
    try {
      const response = await axios.get(`${API_URL}/roles/${id}`);
      const role = response.data;
      return role;
    } catch (error) {
      console.error(`Error fetching role with ID ${id}:`, error);
      throw error;
    }
  }

  async createRole(roleData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: roleData.name,
        status: roleData.status || "active",
        description: roleData.description || null,
      };

      const response = await axios.post(`${API_URL}/roles`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating role:", error);
      throw error;
    }
  }

  async updateRole(id, roleData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: roleData.name,
        status: roleData.status || "active",
        description: roleData.description || null,
      };

      const response = await axios.put(`${API_URL}/roles/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating role with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteRole(id) {
    try {
      const response = await axios.delete(`${API_URL}/roles/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting role with ID ${id}:`, error);
      throw error;
    }
  }

  async changeRoleStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/roles/${id}/status`, {
        status: status, // "active" or "inactive"
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing role status for ID ${id}:`, error);
      throw error;
    }
  }

  async searchRoles(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/roles/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(`Error searching roles with term ${searchTerm}:`, error);
      throw error;
    }
  }

  async getActiveRoles() {
    try {
      const response = await axios.get(`${API_URL}/roles/active`);
      return response.data;
    } catch (error) {
      console.error("Error fetching active roles:", error);
      throw error;
    }
  }

  async getRolesForDropdown() {
    try {
      const response = await axios.get(`${API_URL}/roles`);
      // Return array of roles with id, name, status
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching roles for dropdown:", error);
      throw error;
    }
  }

  async bulkImportRoles(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/roles/insert-roles`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing roles from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const roleService = new RoleService();
export default roleService;
