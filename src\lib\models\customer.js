// Customer model and related data
export const customerTypes = ["Enterprise", "SMB", "Startup", "Individual"];

export const customerStatuses = ["active", "inactive", "pending", "suspended"];

export const industries = [
  "Technology",
  "Healthcare",
  "Finance",
  "Education",
  "Retail",
  "Manufacturing",
  "Real Estate",
  "Media & Entertainment",
  "Automotive",
  "Food & Beverage",
  "Travel & Tourism",
  "Government",
  "Non-Profit",
  "Other",
];

export const companyCategories = [
  "Ad Creative Agency",
  "Digital Display Mnfg.",
  "Digital Printer",
  "DOOH Media owner",
  "Expert speaker",
  "Fabricator",
  "Govt. Authority (Mun.Corp)",
  "LED Manufacturer",
  "Marketer (Client)",
  "Media Buying Agency",
  "Media Owner",
  "Media Planning Agency",
  "OOH Company Intl.",
  "OOH Concessionaire",
];

export const createCustomer = (data) => {
  return {
    id: data.id || null,
    name: data.name || "",
    email: data.email || "",
    phone: data.phone || "",
    address: data.address || "",
    cityId: data.cityId || "",
    state: data.state || "",
    country: data.country || "India",
    type: data.type || "SMB",
    industry: data.industry || "",
    companyCategory: data.companyCategory || "",
    website: data.website || "",
    status: data.status || "active",
    createdAt: data.createdAt || new Date().toISOString(),
    updatedAt: data.updatedAt || new Date().toISOString(),
  };
};

export const getCustomerDisplayName = (customer) => {
  return customer.name || customer.email || "Unknown Customer";
};

export const getCustomerStatusColor = (status) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800";
    case "inactive":
      return "bg-gray-100 text-gray-800";
    case "pending":
      return "bg-yellow-100 text-yellow-800";
    case "suspended":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const sampleCustomers = [
  {
    id: "CUST-001",
    name: "Acme Displays",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    address: "123 Corporate Park",
    city: "Mumbai",
    state: "Maharashtra",
    country: "India",
    status: "active",
    type: "Corporate",
    industry: "Retail",
    website: "www.acmedisplays.com",
    employees: [
      {
        id: "EMP-001",
        name: "Rahul Sharma",
        email: "<EMAIL>",
        phone: "+91 98765 43211",
        designation: "Marketing Director",
        department: "Marketing",
        isPrimary: true,
      },
      {
        id: "EMP-002",
        name: "Priya Patel",
        email: "<EMAIL>",
        phone: "+91 98765 43212",
        designation: "Procurement Manager",
        department: "Procurement",
        isPrimary: false,
      },
    ],
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-09-20T14:15:00Z",
    totalSpent: 350000,
    totalOrders: 5,
    lastPurchase: "2023-09-20",
    tags: ["VIP", "Regular"],
  },
  {
    id: "CUST-002",
    name: "TechVision Inc",
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    address: "456 Tech Park",
    city: "Bangalore",
    state: "Karnataka",
    country: "India",
    status: "active",
    type: "Corporate",
    industry: "Technology",
    website: "www.techvision.com",
    employees: [
      {
        id: "EMP-003",
        name: "Vikram Singh",
        email: "<EMAIL>",
        phone: "+91 87654 32110",
        designation: "CEO",
        department: "Management",
        isPrimary: true,
      },
    ],
    createdAt: "2023-02-20T09:45:00Z",
    updatedAt: "2023-10-05T11:20:00Z",
    totalSpent: 1200000,
    totalOrders: 3,
    lastPurchase: "2023-10-05",
    tags: ["Premium", "Sponsor"],
  },
  {
    id: "CUST-003",
    name: "MediaWorks",
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    address: "789 Media Center",
    city: "Delhi",
    state: "Delhi",
    country: "India",
    status: "active",
    type: "Media",
    industry: "Media & Entertainment",
    website: "www.mediaworks.com",
    employees: [
      {
        id: "EMP-004",
        name: "Anjali Gupta",
        email: "<EMAIL>",
        phone: "+91 76543 21099",
        designation: "Media Buyer",
        department: "Advertising",
        isPrimary: true,
      },
    ],
    createdAt: "2023-03-10T14:20:00Z",
    totalSpent: 250000,
    totalOrders: 4,
    lastPurchase: "2023-08-15",
    notes: "Prefers email communication",
    tags: ["Media", "Regular"],
  },
  {
    id: "CUST-004",
    name: "SignagePro",
    email: "<EMAIL>",
    phone: "+91 65432 10987",
    address: "101 Business Hub",
    city: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    status: "active",
    type: "Retail",
    industry: "Signage & Printing",
    website: "www.signagepro.com",
    employees: [
      {
        id: "EMP-005",
        name: "Karthik Rajan",
        email: "<EMAIL>",
        phone: "+91 65432 10988",
        designation: "Purchase Manager",
        department: "Procurement",
        isPrimary: true,
      },
    ],
    createdAt: "2023-04-05T11:10:00Z",
    updatedAt: "2023-09-12T10:30:00Z",
    totalSpent: 180000,
    totalOrders: 2,
    lastPurchase: "2023-09-12",
    tags: ["New Client"],
  },
  {
    id: "CUST-005",
    name: "DigitalAds Co",
    email: "<EMAIL>",
    phone: "+91 54321 09876",
    address: "202 Digital Zone",
    city: "Hyderabad",
    state: "Telangana",
    country: "India",
    status: "pending",
    type: "Agency",
    industry: "Advertising",
    website: "www.digitalads.com",
    employees: [
      {
        id: "EMP-006",
        name: "Rohit Kapoor",
        email: "<EMAIL>",
        phone: "+91 54321 09877",
        designation: "Creative Director",
        department: "Creative",
        isPrimary: true,
      },
    ],
    createdAt: "2023-05-12T16:40:00Z",
    totalSpent: 0,
    totalOrders: 0,
    notes: "Interested in upcoming events",
    tags: ["Prospect"],
  },
];
