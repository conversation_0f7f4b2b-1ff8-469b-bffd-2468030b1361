"use client";

import { useState, useEffect } from "react";
import {
  Plus,
  MoreHorizontal,
  Pencil,
  Trash2,
  Factory,
  Calendar,
  User,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { ProductionListForm } from "@/components/forms/production-list-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import productionListService from "../../../services/production-lists.service";

export default function ProductionLists() {
  const { toast } = useToast();
  const [productionLists, setProductionLists] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProductionList, setSelectedProductionList] = useState(null);

  // Fetch production lists from API using the service
  const fetchProductionLists = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await productionListService.getAllProductionLists({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const productionListsArray = Array.isArray(result.productionLists)
        ? result.productionLists
        : [];
      setProductionLists(productionListsArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch production lists",
        variant: "destructive",
      });
      console.error("Error fetching production lists:", error);
      setProductionLists([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProductionLists();
  }, []);

  const handleAddProductionList = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditProductionList = (productionList) => {
    setSelectedProductionList(productionList);
    setIsEditDialogOpen(true);
  };

  const handleDeleteProductionList = (productionList) => {
    setSelectedProductionList(productionList);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateProductionList = async (data) => {
    try {
      await productionListService.createProductionList(data);
      await fetchProductionLists();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Production list created successfully",
      });
    } catch (error) {
      console.error("Create production list error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create production list",
        variant: "destructive",
      });
    }
  };

  const handleUpdateProductionList = async (data) => {
    try {
      await productionListService.updateProductionList(
        selectedProductionList.id,
        data
      );
      await fetchProductionLists();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Production list updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update production list",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteProductionList = async () => {
    try {
      await productionListService.deleteProductionList(
        selectedProductionList.id
      );
      await fetchProductionLists();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Production list deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete production list",
        variant: "destructive",
      });
    }
  };

  const handleImportProductionLists = async (file) => {
    try {
      const result = await productionListService.bulkImportProductionLists(
        file
      );

      await fetchProductionLists(); // Refresh the production lists
      toast({
        title: "Success",
        description: "Production lists imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import production lists from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { className: "bg-yellow-500", label: "Pending" },
      "in-progress": { className: "bg-blue-500", label: "In Progress" },
      completed: { className: "bg-emerald-500", label: "Completed" },
      cancelled: { className: "bg-red-500", label: "Cancelled" },
    };

    const config = statusConfig[status] || {
      className: "bg-gray-500",
      label: status,
    };
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const columns = [
    {
      accessorKey: "productionSheetNo",
      header: "Sheet No",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <Factory className="h-4 w-4 text-muted-foreground" />
          {row.getValue("productionSheetNo")}
        </div>
      ),
    },
    {
      accessorKey: "quantity",
      header: "Quantity",
    },
    {
      accessorKey: "vehicleType",
      header: "Vehicle Type",
    },
    {
      accessorKey: "operator",
      header: "Operator",
      cell: ({ row }) => {
        const operator = row.original.operator;
        return (
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            {operator ? operator.name : "N/A"}
          </div>
        );
      },
    },
    {
      accessorKey: "appointmentDate",
      header: "Appointment",
      cell: ({ row }) => {
        const date = new Date(row.getValue("appointmentDate"));
        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear();

        return (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            {`${day}-${month}-${year}`}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.getValue("status")),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const productionList = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => handleEditProductionList(productionList)}
              >
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteProductionList(productionList)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Production Lists Management</CardTitle>
              <CardDescription>
                Manage production schedules, operators, and vehicle assignments
              </CardDescription>
            </div>
            <Button onClick={handleAddProductionList}>
              <Plus className="mr-2 h-4 w-4" />
              Add Production List
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={productionLists}
            searchKey="productionSheetNo"
            isLoading={isLoading}
            entityName="Production Lists"
            onImport={handleImportProductionLists}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/production_lists_template.xlsx"
            fileDescription="File should contain columns: productionSheetNo, quantity, operatorId, vehicleType, appointmentDate, status, notes"
          />
        </CardContent>
      </Card>

      {/* Add Production List Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Production List</DialogTitle>
            <DialogDescription>
              Create a new production list with operator and vehicle details.
            </DialogDescription>
          </DialogHeader>
          <ProductionListForm
            onSubmit={handleCreateProductionList}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Production List Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Production List</DialogTitle>
            <DialogDescription>
              Update the production list information and details.
            </DialogDescription>
          </DialogHeader>
          {selectedProductionList && (
            <ProductionListForm
              defaultValues={{
                ...selectedProductionList,
                appointmentDate: selectedProductionList.appointmentDate
                  ? new Date(selectedProductionList.appointmentDate)
                      .toISOString()
                      .slice(0, 16)
                  : "",
              }}
              onSubmit={handleUpdateProductionList}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Production List Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete production list "
              {selectedProductionList?.productionSheetNo}"? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteProductionList}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
