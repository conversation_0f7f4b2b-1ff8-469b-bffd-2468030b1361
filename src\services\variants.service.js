import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class VariantsService {
  async getAllVariants(
    params = { page: 1, limit: 100, search: "", status: "" }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/variants?${queryString}`
        : `${API_URL}/variants`;

      const response = await axios.get(url);

      // Ensure data is in expected format and transform status to isActive
      const variantsData = Array.isArray(response.data) ? response.data : [];
      const transformedData = variantsData.map((item) => ({
        ...item,
        isActive: item.status === "active", // Convert status enum to boolean for frontend
      }));

      return {
        variants: transformedData,
      };
    } catch (error) {
      console.error("Error fetching variants:", error);
      throw error;
    }
  }

  async getVariantById(id) {
    try {
      const response = await axios.get(`${API_URL}/variants/${id}`);
      const variant = response.data;

      // Transform status to isActive for frontend compatibility
      return {
        ...variant,
        isActive: variant.status === "active",
      };
    } catch (error) {
      console.error(`Error fetching variant with ID ${id}:`, error);
      throw error;
    }
  }

  async createVariant(variantData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: variantData.name,
        slug: this.generateSlug(variantData.name),
        code: variantData.code || this.generateCode(variantData.name),
        description: variantData.description || "",
        propertyId: variantData.propertyId,
        status: variantData.isActive ? "active" : "inactive", // Convert boolean to enum
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/variants`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating variant:", error);
      throw error;
    }
  }

  async updateVariant(id, variantData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: variantData.name,
        slug: this.generateSlug(variantData.name),
        code: variantData.code || this.generateCode(variantData.name),
        description: variantData.description || "",
        propertyId: variantData.propertyId,
        status: variantData.isActive ? "active" : "inactive", // Convert boolean to enum
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/variants/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating variant with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteVariant(id) {
    try {
      const response = await axios.delete(`${API_URL}/variants/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting variant with ID ${id}:`, error);
      throw error;
    }
  }

  // Helper method to get properties for dropdown
  async getProperties() {
    try {
      const response = await axios.get(`${API_URL}/properties`);
      const propertiesData = Array.isArray(response.data) ? response.data : [];
      return propertiesData.filter(
        (property) => property.status === "active" || property.isActive
      );
    } catch (error) {
      console.error("Error fetching properties:", error);
      return [];
    }
  }

  // Helper method to generate slug from name
  generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }

  // Helper method to generate code from name
  generateCode(name) {
    const timestamp = Date.now().toString().slice(-4);
    const nameCode = name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, "")
      .slice(0, 4);
    return `VAR-${nameCode}-${timestamp}`;
  }

  async bulkImportVariants(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/variants/insert-variants`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing variants from Excel:", error);
      throw error;
    }
  }
}

// Export a singleton instance
const variantsService = new VariantsService();
export default variantsService;
