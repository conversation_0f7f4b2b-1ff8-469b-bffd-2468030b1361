export interface City {
  id: string
  name: string
  state: string
  country: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

export const sampleCities: City[] = [
  {
    id: "CITY-001",
    name: "Mumbai",
    state: "Maharashtra",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CITY-002",
    name: "Delhi",
    state: "Delhi",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CITY-003",
    name: "Bangalore",
    state: "Karnataka",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CITY-004",
    name: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CITY-005",
    name: "Hyderabad",
    state: "Telangana",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CITY-006",
    name: "Pune",
    state: "Maharashtra",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CITY-007",
    name: "Kolkata",
    state: "West Bengal",
    country: "India",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
]
