import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class CompanyCategoryService {
  async getAllCompanyCategories(
    params = {
      page: 1,
      limit: 100,
      search: "",
      status: "",
    }
  ) {
    try {
      const response = await axios.get(`${API_URL}/company-categories`, {
        params: {
          page: params.page || 1,
          limit: params.limit || 100,
          search: params.search || "",
          status: params.status || "",
        },
      });

      // Handle different response formats
      if (response.data && response.data.data) {
        // If response has nested data structure
        return {
          companyCategories: response.data.data.companyCategories || response.data.data,
          total: response.data.data.total || response.data.total,
          page: response.data.data.page || response.data.page,
          limit: response.data.data.limit || response.data.limit,
        };
      } else if (response.data && Array.isArray(response.data.companyCategories)) {
        // If response has companyCategories array directly
        return response.data;
      } else if (response.data && Array.isArray(response.data)) {
        // If response is directly an array
        return {
          companyCategories: response.data,
          total: response.data.length,
          page: 1,
          limit: response.data.length,
        };
      } else {
        // Fallback
        return {
          companyCategories: [],
          total: 0,
          page: 1,
          limit: 100,
        };
      }
    } catch (error) {
      console.error("Error fetching company categories:", error);
      throw error;
    }
  }

  async createCompanyCategory(companyCategoryData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: companyCategoryData.name,
        status: companyCategoryData.status || "active",
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/company-categories`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating company category:", error);
      throw error;
    }
  }

  async updateCompanyCategory(id, companyCategoryData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: companyCategoryData.name,
        status: companyCategoryData.status || "active",
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/company-categories/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating company category with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteCompanyCategory(id) {
    try {
      const response = await axios.delete(`${API_URL}/company-categories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting company category with ID ${id}:`, error);
      throw error;
    }
  }

  async getCompanyCategoryById(id) {
    try {
      const response = await axios.get(`${API_URL}/company-categories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching company category with ID ${id}:`, error);
      throw error;
    }
  }

  async importCompanyCategories(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(`${API_URL}/company-categories/import`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error importing company categories:", error);
      throw error;
    }
  }

  async exportCompanyCategories(format = "xlsx") {
    try {
      const response = await axios.get(`${API_URL}/company-categories/export`, {
        params: { format },
        responseType: "blob",
      });
      return response.data;
    } catch (error) {
      console.error(`Error exporting company categories in ${format} format:`, error);
      throw error;
    }
  }

  async searchCompanyCategories(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/company-categories/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(`Error searching company categories with term ${searchTerm}:`, error);
      throw error;
    }
  }

  // Utility method to validate company category data
  validateCompanyCategoryData(companyCategoryData) {
    const errors = {};

    if (!companyCategoryData.name || companyCategoryData.name.trim().length === 0) {
      errors.name = "Company category name is required";
    }

    if (companyCategoryData.name && companyCategoryData.name.trim().length < 2) {
      errors.name = "Company category name must be at least 2 characters";
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }
}

// Export a singleton instance
const companyCategoryService = new CompanyCategoryService();
export default companyCategoryService;
