"use client"

import { useForm } from "react-hook-form"
import { But<PERSON> } from "../ui/button.jsx"
import { Input } from "../ui/input.jsx"
import { Label } from "../ui/label.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select.jsx"
import { Textarea } from "../ui/textarea.jsx"
import { mediums, channels, properties, variants } from "../../lib/models/transaction.js"

export function TransactionForm({ customerId, defaultValues, onSubmit, onCancel }) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: defaultValues || {
      customerId: customerId || "",
      amount: "",
      date: new Date().toISOString().split("T")[0],
      description: "",
      medium: "",
      channel: "",
      property: "",
      variant: "",
    },
  })

  const handleFormSubmit = async (data) => {
    await onSubmit({
      ...data,
      amount: Number.parseFloat(data.amount),
    })
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount *</Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            min="0"
            {...register("amount", {
              required: "Amount is required",
              min: { value: 0, message: "Amount must be positive" },
            })}
            placeholder="Enter amount"
          />
          {errors.amount && <p className="text-sm text-red-500">{errors.amount.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="date">Date *</Label>
          <Input id="date" type="date" {...register("date", { required: "Date is required" })} />
          {errors.date && <p className="text-sm text-red-500">{errors.date.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="medium">Medium *</Label>
          <Select onValueChange={(value) => setValue("medium", value)} defaultValue={watch("medium")}>
            <SelectTrigger>
              <SelectValue placeholder="Select medium" />
            </SelectTrigger>
            <SelectContent>
              {mediums.map((medium) => (
                <SelectItem key={medium} value={medium}>
                  {medium}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.medium && <p className="text-sm text-red-500">{errors.medium.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="channel">Channel *</Label>
          <Select onValueChange={(value) => setValue("channel", value)} defaultValue={watch("channel")}>
            <SelectTrigger>
              <SelectValue placeholder="Select channel" />
            </SelectTrigger>
            <SelectContent>
              {channels.map((channel) => (
                <SelectItem key={channel} value={channel}>
                  {channel}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.channel && <p className="text-sm text-red-500">{errors.channel.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="property">Property *</Label>
          <Select onValueChange={(value) => setValue("property", value)} defaultValue={watch("property")}>
            <SelectTrigger>
              <SelectValue placeholder="Select property" />
            </SelectTrigger>
            <SelectContent>
              {properties.map((property) => (
                <SelectItem key={property} value={property}>
                  {property}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.property && <p className="text-sm text-red-500">{errors.property.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="variant">Variant *</Label>
          <Select onValueChange={(value) => setValue("variant", value)} defaultValue={watch("variant")}>
            <SelectTrigger>
              <SelectValue placeholder="Select variant" />
            </SelectTrigger>
            <SelectContent>
              {variants.map((variant) => (
                <SelectItem key={variant} value={variant}>
                  {variant}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.variant && <p className="text-sm text-red-500">{errors.variant.message}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          {...register("description", { required: "Description is required" })}
          placeholder="Enter transaction description"
          rows={3}
        />
        {errors.description && <p className="text-sm text-red-500">{errors.description.message}</p>}
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : defaultValues ? "Update Transaction" : "Create Transaction"}
        </Button>
      </div>
    </form>
  )
}
