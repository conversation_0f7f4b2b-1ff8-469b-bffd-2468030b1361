"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Tabs, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Award, Trophy, Star, Calendar, TrendingUp } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts"
import { PageHeader } from "../../components/page-header.jsx"
import { DataTable } from "../../components/data-table.jsx"

const awardsData = [
  {
    id: 1,
    title: "Best Customer Service",
    category: "Service Excellence",
    year: 2024,
    organization: "Customer Service Institute",
    status: "Won",
    description: "Recognized for outstanding customer satisfaction scores",
    date: "2024-03-15",
  },
  {
    id: 2,
    title: "Innovation Award",
    category: "Technology",
    year: 2024,
    organization: "Tech Innovation Council",
    status: "Nominated",
    description: "For breakthrough AI-powered analytics platform",
    date: "2024-05-20",
  },
  {
    id: 3,
    title: "Sustainability Champion",
    category: "Environmental",
    year: 2023,
    organization: "Green Business Alliance",
    status: "Won",
    description: "Leading sustainable business practices",
    date: "2023-11-10",
  },
  {
    id: 4,
    title: "Employer of the Year",
    category: "Workplace",
    year: 2023,
    organization: "HR Excellence Awards",
    status: "Won",
    description: "Outstanding employee satisfaction and growth",
    date: "2023-09-22",
  },
  {
    id: 5,
    title: "Digital Transformation",
    category: "Technology",
    year: 2024,
    organization: "Digital Leaders Forum",
    status: "Pending",
    description: "Successful digital transformation initiative",
    date: "2024-07-15",
  },
]

const categoryData = [
  { name: "Technology", count: 2, color: "#8884d8" },
  { name: "Service Excellence", count: 1, color: "#82ca9d" },
  { name: "Environmental", count: 1, color: "#ffc658" },
  { name: "Workplace", count: 1, color: "#ff7300" },
]

const yearlyData = [
  { year: "2020", won: 2, nominated: 1 },
  { year: "2021", won: 3, nominated: 2 },
  { year: "2022", won: 1, nominated: 3 },
  { year: "2023", won: 2, nominated: 1 },
  { year: "2024", won: 1, nominated: 2 },
]

const columns = [
  {
    accessorKey: "title",
    header: "Award Title",
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }) => <Badge variant="outline">{row.getValue("category")}</Badge>,
  },
  {
    accessorKey: "organization",
    header: "Organization",
  },
  {
    accessorKey: "year",
    header: "Year",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status")
      return (
        <Badge variant={status === "Won" ? "default" : status === "Nominated" ? "secondary" : "outline"}>
          {status}
        </Badge>
      )
    },
  },
]

export default function AwardsPage() {
  const [selectedAward, setSelectedAward] = useState(null)
  const [filter, setFilter] = useState("all")

  const totalAwards = awardsData.length
  const wonAwards = awardsData.filter((award) => award.status === "Won").length
  const nominatedAwards = awardsData.filter((award) => award.status === "Nominated").length
  const pendingAwards = awardsData.filter((award) => award.status === "Pending").length

  const filteredAwards =
    filter === "all" ? awardsData : awardsData.filter((award) => award.status.toLowerCase() === filter)

  return (
    <div className="space-y-6">
      <PageHeader title="Awards & Recognition" description="Track and manage organizational awards and achievements" />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Awards</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAwards}</div>
            <p className="text-xs text-muted-foreground">All time achievements</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Awards Won</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{wonAwards}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                {((wonAwards / totalAwards) * 100).toFixed(0)}% success rate
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nominations</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{nominatedAwards}</div>
            <p className="text-xs text-muted-foreground">Active nominations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingAwards}</div>
            <p className="text-xs text-muted-foreground">Awaiting results</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex space-x-2">
        <Button variant={filter === "all" ? "default" : "outline"} onClick={() => setFilter("all")}>
          All Awards
        </Button>
        <Button variant={filter === "won" ? "default" : "outline"} onClick={() => setFilter("won")}>
          Won
        </Button>
        <Button variant={filter === "nominated" ? "default" : "outline"} onClick={() => setFilter("nominated")}>
          Nominated
        </Button>
        <Button variant={filter === "pending" ? "default" : "outline"} onClick={() => setFilter("pending")}>
          Pending
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="awards">All Awards</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Awards by Category</CardTitle>
                <CardDescription>Distribution of awards across categories</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="count"
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="flex justify-center space-x-4 mt-4">
                  {categoryData.map((category, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: category.color }} />
                      <span className="text-sm">{category.name}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Achievements</CardTitle>
                <CardDescription>Latest awards and recognitions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {awardsData
                  .filter((award) => award.status === "Won")
                  .slice(0, 3)
                  .map((award) => (
                    <div key={award.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <Trophy className="h-5 w-5 text-yellow-500 mt-1" />
                      <div className="flex-1">
                        <div className="font-medium">{award.title}</div>
                        <div className="text-sm text-muted-foreground">{award.organization}</div>
                        <div className="text-xs text-muted-foreground">{award.date}</div>
                      </div>
                      <Badge variant="default">Won</Badge>
                    </div>
                  ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="awards" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Awards & Recognition</CardTitle>
              <CardDescription>Complete list of organizational awards and achievements</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={filteredAwards} />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredAwards.map((award) => (
              <Card key={award.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      {award.status === "Won" ? (
                        <Trophy className="h-5 w-5 text-yellow-500" />
                      ) : award.status === "Nominated" ? (
                        <Star className="h-5 w-5 text-blue-500" />
                      ) : (
                        <Award className="h-5 w-5 text-gray-500" />
                      )}
                      <CardTitle className="text-lg">{award.title}</CardTitle>
                    </div>
                    <Badge
                      variant={
                        award.status === "Won" ? "default" : award.status === "Nominated" ? "secondary" : "outline"
                      }
                    >
                      {award.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    {award.category} • {award.year}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">{award.organization}</div>
                    <div className="text-sm text-muted-foreground">{award.description}</div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3 mr-1" />
                      {award.date}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Awards Trends</CardTitle>
              <CardDescription>Awards won and nominations over the years</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={yearlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="won" fill="#8884d8" name="Awards Won" />
                  <Bar dataKey="nominated" fill="#82ca9d" name="Nominations" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Awards Timeline</CardTitle>
              <CardDescription>Chronological view of all awards and achievements</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {awardsData
                  .sort((a, b) => new Date(b.date) - new Date(a.date))
                  .map((award, index) => (
                    <div key={award.id} className="flex items-start space-x-4 pb-4 border-b last:border-b-0">
                      <div className="flex-shrink-0">
                        {award.status === "Won" ? (
                          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <Trophy className="h-4 w-4 text-yellow-600" />
                          </div>
                        ) : award.status === "Nominated" ? (
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <Star className="h-4 w-4 text-blue-600" />
                          </div>
                        ) : (
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <Award className="h-4 w-4 text-gray-600" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">{award.title}</h3>
                          <Badge
                            variant={
                              award.status === "Won"
                                ? "default"
                                : award.status === "Nominated"
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {award.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{award.description}</p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                          <span>{award.organization}</span>
                          <span>•</span>
                          <span>{award.category}</span>
                          <span>•</span>
                          <span>{award.date}</span>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
