import {
  AreaChart as Re<PERSON>rts<PERSON><PERSON><PERSON><PERSON>,
  Area,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { cn } from "@/lib/utils"

export function AreaChart({
  data,
  categories,
  index,
  className,
  colors = ["#3b82f6", "#ef4444", "#10b981", "#f59e0b", "#8b5cf6"],
  valueFormatter,
  yAxisWidth = 56,
  ...props
}) {
  const categoryKeys = Object.keys(categories || {})

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsAreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }} {...props}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={index} />
          <YAxis width={yAxisWidth} tickFormatter={valueF<PERSON><PERSON>er} />
          <Tooltip formatter={valueFormatter} />
          <Legend />
          {categoryKeys.map((key, idx) => (
            <Area
              key={key}
              type="monotone"
              dataKey={key}
              stackId="1"
              stroke={colors[idx % colors.length]}
              fill={colors[idx % colors.length]}
              name={categories[key]?.label || key}
            />
          ))}
        </RechartsAreaChart>
      </ResponsiveContainer>
    </div>
  )
}
