import type { ApiResponse, ApiRequestOptions } from "@/lib/types/api"

class ApiClient {
  private baseUrl: string

  constructor(baseUrl = "/api") {
    this.baseUrl = baseUrl
  }

  private async request<T>(endpoint: string, options: ApiRequestOptions = { method: "GET" }): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`

    const config: RequestInit = {
      method: options.method,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      // Include credentials to send cookies with requests
      credentials: "include",
    }

    if (options.body && options.method !== "GET") {
      config.body = JSON.stringify(options.body)
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
          // Redirect to login if unauthorized
          if (typeof window !== "undefined") {
            window.location.href = "/login"
          }
        }
        throw new Error(data.error || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error("API request failed:", error)
      throw error
    }
  }

  // Generic CRUD methods
  async get<T>(endpoint: string, params?: Record<string, string>): Promise<ApiResponse<T>> {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ""
    return this.request<T>(`${endpoint}${searchParams}`)
  }

  async post<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data,
    })
  }

  async put<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "DELETE",
    })
  }

  // Auth methods
  auth = {
    login: (email: string, password: string) =>
      this.post<{ user: any; token: string }>("/auth/login", { email, password }),
    logout: () => this.post("/auth/logout", {}),
    me: () => this.get("/auth/me"),
  }

  // Specific API methods
  customers = {
    getAll: (params?: Record<string, string>) => this.get("/customers", params),
    getById: (id: string) => this.get(`/customers/${id}`),
    create: (data: any) => this.post("/customers", data),
    update: (id: string, data: any) => this.put(`/customers/${id}`, data),
    delete: (id: string) => this.delete(`/customers/${id}`),
  }

  transactions = {
    getAll: (params?: Record<string, string>) => this.get("/transactions", params),
    getById: (id: string) => this.get(`/transactions/${id}`),
    create: (data: any) => this.post("/transactions", data),
    update: (id: string, data: any) => this.put(`/transactions/${id}`, data),
    delete: (id: string) => this.delete(`/transactions/${id}`),
  }

  masters = {
    mediums: {
      getAll: (params?: Record<string, string>) => this.get("/masters/mediums", params),
      create: (data: any) => this.post("/masters/mediums", data),
    },
    channels: {
      getAll: (params?: Record<string, string>) => this.get("/masters/channels", params),
      create: (data: any) => this.post("/masters/channels", data),
    },
    properties: {
      getAll: (params?: Record<string, string>) => this.get("/masters/properties", params),
      create: (data: any) => this.post("/masters/properties", data),
    },
    variants: {
      getAll: (params?: Record<string, string>) => this.get("/masters/variants", params),
      create: (data: any) => this.post("/masters/variants", data),
    },
  }

  users = {
    getAll: (params?: Record<string, string>) => this.get("/users", params),
    getById: (id: string) => this.get(`/users/${id}`),
    create: (data: any) => this.post("/users", data),
    update: (id: string, data: any) => this.put(`/users/${id}`, data),
    delete: (id: string) => this.delete(`/users/${id}`),
  }

  customerJourney = {
    getAll: (params?: Record<string, string>) => this.get("/customer-journey", params),
    getById: (id: string) => this.get(`/customer-journey/${id}`),
    create: (data: any) => this.post("/customer-journey", data),
    update: (id: string, data: any) => this.put(`/customer-journey/${id}`, data),
    delete: (id: string) => this.delete(`/customer-journey/${id}`),
  }
}

export const api = new ApiClient()
