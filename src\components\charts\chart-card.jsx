import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function ChartCard({ title, description, children, className, ...props }) {
  return (
    <Card className={className} {...props}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  )
}
