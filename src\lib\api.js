// Mock API service for React.js version
const API_BASE_URL = "http://localhost:3001/api"

class ApiService {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`
    const config = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    }

    if (config.body && typeof config.body === "object") {
      config.body = JSON.stringify(config.body)
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error("API request failed:", error)
      throw error
    }
  }

  // Customer endpoints
  async getCustomers() {
    return this.request("/customers")
  }

  async getCustomer(id) {
    return this.request(`/customers/${id}`)
  }

  async createCustomer(data) {
    return this.request("/customers", {
      method: "POST",
      body: data,
    })
  }

  async updateCustomer(id, data) {
    return this.request(`/customers/${id}`, {
      method: "PUT",
      body: data,
    })
  }

  async deleteCustomer(id) {
    return this.request(`/customers/${id}`, {
      method: "DELETE",
    })
  }

  // Transaction endpoints
  async getTransactions() {
    return this.request("/transactions")
  }

  async createTransaction(data) {
    return this.request("/transactions", {
      method: "POST",
      body: data,
    })
  }

  // Master data endpoints
  async getMasterData(type) {
    return this.request(`/masters/${type}`)
  }

  async createMasterData(type, data) {
    return this.request(`/masters/${type}`, {
      method: "POST",
      body: data,
    })
  }

  async updateMasterData(type, id, data) {
    return this.request(`/masters/${type}/${id}`, {
      method: "PUT",
      body: data,
    })
  }

  async deleteMasterData(type, id) {
    return this.request(`/masters/${type}/${id}`, {
      method: "DELETE",
    })
  }

  // Analytics endpoints
  async getDashboardData() {
    return this.request("/analytics/dashboard")
  }

  async getRevenueData() {
    return this.request("/analytics/revenue")
  }

  async getDemographicsData() {
    return this.request("/analytics/demographics")
  }
}

export const apiService = new ApiService()
