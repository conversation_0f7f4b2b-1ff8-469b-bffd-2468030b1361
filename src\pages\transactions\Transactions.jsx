
"use client";

import { useState, useEffect } from "react";
import { Plus, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { But<PERSON> } from "../../components/ui/button.jsx";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card.jsx";
import { Badge } from "../../components/ui/badge.jsx";
import { DataTable } from "../../components/data-table.jsx";
import { TransactionForm } from "../../components/forms/transaction-form.jsx";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog.jsx";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu.jsx";
import { useToast } from "../../hooks/use-toast.js";
import transactionService from "../../services/transaction.service.js";
import { formatCurrency, formatDate } from "../../lib/utils.js";

export default function TransactionsPage() {
  const { toast } = useToast();
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  // Fetch transactions from API using the service
  const fetchTransactions = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await transactionService.getAllTransactions({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const transactionsArray = Array.isArray(result.transactions)
        ? result.transactions
        : [];
      setTransactions(transactionsArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch transactions",
        variant: "destructive",
      });
      console.error("Error fetching transactions:", error);
      setTransactions([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, []);

  const handleAddTransaction = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditTransaction = (transaction) => {
    setSelectedTransaction(transaction);
    setIsEditDialogOpen(true);
  };

  const handleDeleteTransaction = (transaction) => {
    setSelectedTransaction(transaction);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateTransaction = async (data) => {
    try {
      await transactionService.createTransaction(data);
      await fetchTransactions();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Transaction created successfully",
      });
    } catch (error) {
      console.error("Create transaction error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create transaction",
        variant: "destructive",
      });
    }
  };

  const handleUpdateTransaction = async (data) => {
    try {
      await transactionService.updateTransaction(selectedTransaction.id, data);
      await fetchTransactions();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Transaction updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update transaction",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteTransaction = async () => {
    try {
      await transactionService.deleteTransaction(selectedTransaction.id);
      await fetchTransactions();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Transaction deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete transaction",
        variant: "destructive",
      });
    }
  };

  const handleImportTransactions = async (file) => {
    try {
      const result = await transactionService.bulkImportTransactions(file);

      await fetchTransactions(); // Refresh the transactions list
      toast({
        title: "Success",
        description: "Transactions imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import transactions from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const columns = [
   
    {
      accessorKey: "customerInfo",
      header: "Company",
      cell: ({ row }) => {
        const customerInfo = row.getValue("customerInfo");
        return <div className="font-medium">{customerInfo?.name || "N/A"}</div>;
      },
    },
    {
      accessorKey: "mediumInfo",
      header: "Medium",
      cell: ({ row }) => {
        const mediumInfo = row.getValue("mediumInfo");
        return <div>{mediumInfo?.name || "N/A"}</div>;
      },
    },
    {
      accessorKey: "channelInfo",
      header: "Channel",
      cell: ({ row }) => {
        const channelInfo = row.getValue("channelInfo");
        return <div>{channelInfo?.name || "N/A"}</div>;
      },
    },
    {
      accessorKey: "propertyInfo",
      header: "Property",
      cell: ({ row }) => {
        const propertyInfo = row.getValue("propertyInfo");
        return <div>{propertyInfo?.name || "N/A"}</div>;
      },
    },
    {
      accessorKey: "variantInfo",
      header: "Variant",
      cell: ({ row }) => {
        const variantInfo = row.getValue("variantInfo");
        return <div>{variantInfo?.name || "-"}</div>;
      },
    },
    {
      accessorKey: "stage",
      header: "Stage",
      cell: ({ row }) => {
        const stage = row.getValue("stage");
        return <div>{stage || "-"}</div>;
      },
    },
    {
      accessorKey: "month",
      header: "Month",
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          {`${row.getValue("month")}` || "-"}
        </div>
      ),
    },
    {
      accessorKey: "year",
      header: "Year",
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          {`${row.getValue("year")}` || "-"}
        </div>
      ),
    },
    {
      accessorKey: "revenue",
      header: "Revenue",
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          {row.getValue("revenue") || "-"}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        const getStatusColor = (status) => {
          switch (status) {
            case "active":
              return "default";
            case "inactive":
              return "secondary";
            case "cancelled":
              return "destructive";
            default:
              return "secondary";
          }
        };
        return (
          <Badge variant={getStatusColor(status)}>
            {status?.charAt(0).toUpperCase() + status?.slice(1)}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const transaction = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => handleEditTransaction(transaction)}
              >
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteTransaction(transaction)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Transaction Management</CardTitle>
              <CardDescription>
                Manage and track all financial transactions
              </CardDescription>
            </div>
            <Button onClick={handleAddTransaction}>
              <Plus className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={transactions}
            searchKeys={["customerInfo", "mediumInfo", "channelInfo", "propertyInfo", "variantInfo", "stage"]}
            isLoading={isLoading}
            entityName="Transactions"
            onImport={handleImportTransactions}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/transactions_template.xlsx"
            fileDescription="File should contain columns: customerId, mediumId, channelId, propertyId, variantId, cityId, stage, transactionDate, amount, taxAmount, totalAmount, paymentStatus, paymentMethod, invoiceNumber, description, status"
          />
        </CardContent>
      </Card>

      {/* Add Transaction Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Add New Transaction</DialogTitle>
            <DialogDescription>
              Create a new transaction record with all details.
            </DialogDescription>
          </DialogHeader>
          <TransactionForm
            onSubmit={handleCreateTransaction}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Transaction Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Edit Transaction</DialogTitle>
            <DialogDescription>
              Update the transaction information.
            </DialogDescription>
          </DialogHeader>
          {selectedTransaction && (
            <TransactionForm
              defaultValues={selectedTransaction}
              onSubmit={handleUpdateTransaction}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Transaction Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this transaction? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteTransaction}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
