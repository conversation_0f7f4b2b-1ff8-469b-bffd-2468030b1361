export interface Medium {
  id: string
  name: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

export interface Channel {
  id: string
  name: string
  mediumId: string
  mediumName: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
  startDate?: string
  endDate?: string
  location?: string
}

export interface Property {
  id: string
  name: string
  channelId: string
  channelName: string
  mediumId: string
  mediumName: string
  description?: string
  isActive: boolean
  price?: number
  currency?: string
  createdAt: string
  updatedAt?: string
}

export interface Variant {
  id: string
  name: string
  propertyId: string
  propertyName: string
  channelId: string
  channelName: string
  mediumId: string
  mediumName: string
  description?: string
  isActive: boolean
  price?: number
  currency?: string
  benefits?: string[]
  createdAt: string
  updatedAt?: string
}

// Sample Medium data
export const sampleMediums: Medium[] = [
  {
    id: "MED-001",
    name: "Event",
    description: "Physical events including exhibitions, conferences, and awards ceremonies",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "MED-002",
    name: "Print",
    description: "Print media including magazines, brochures, and catalogs",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "MED-003",
    name: "Web",
    description: "Digital media including websites, social media, and email marketing",
    isActive: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
]

// Sample Channel data
export const sampleChannels: Channel[] = [
  {
    id: "CHA-001",
    name: "DDX Asia",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Digital display exhibition for Asia region",
    isActive: true,
    createdAt: "2023-01-15T00:00:00Z",
    startDate: "2023-11-15",
    endDate: "2023-11-17",
    location: "Mumbai Exhibition Center",
  },
  {
    id: "CHA-002",
    name: "OAC",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Outdoor Advertising Convention",
    isActive: true,
    createdAt: "2023-01-20T00:00:00Z",
    startDate: "2023-12-10",
    endDate: "2023-12-12",
    location: "Delhi Convention Center",
  },
  {
    id: "CHA-003",
    name: "Talks OAH",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Outdoor Advertising Talks and Seminars",
    isActive: true,
    createdAt: "2023-02-05T00:00:00Z",
    startDate: "2024-01-20",
    endDate: "2024-01-22",
    location: "Bangalore Conference Center",
  },
  {
    id: "CHA-005",
    name: "OA Magazine",
    mediumId: "MED-002",
    mediumName: "Print",
    description: "Monthly magazine focused on outdoor advertising industry",
    isActive: true,
    createdAt: "2023-01-25T00:00:00Z",
  },
  {
    id: "CHA-010",
    name: "R4G Website",
    mediumId: "MED-003",
    mediumName: "Web",
    description: "Official R4G website with industry news and resources",
    isActive: true,
    createdAt: "2023-01-30T00:00:00Z",
  },
]

// Sample Property data
export const sampleProperties: Property[] = [
  {
    id: "PROP-001",
    name: "Exhibitor",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Exhibitor spaces at DDX Asia",
    isActive: true,
    createdAt: "2023-02-01T00:00:00Z",
  },
  {
    id: "PROP-002",
    name: "Award Entry",
    channelId: "CHA-002",
    channelName: "OAC",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Entries for OAC Awards",
    isActive: true,
    createdAt: "2023-02-05T00:00:00Z",
  },
  {
    id: "PROP-003",
    name: "Delegate",
    channelId: "CHA-003",
    channelName: "Talks OAH",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Delegate passes for Talks OAH",
    isActive: true,
    createdAt: "2023-02-10T00:00:00Z",
  },
  {
    id: "PROP-005",
    name: "Sponsor",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Sponsorship packages for DDX Asia",
    isActive: true,
    createdAt: "2023-02-15T00:00:00Z",
  },
  {
    id: "PROP-010",
    name: "Advertisement",
    channelId: "CHA-005",
    channelName: "OA Magazine",
    mediumId: "MED-002",
    mediumName: "Print",
    description: "Print advertisements in OA Magazine",
    isActive: true,
    createdAt: "2023-02-20T00:00:00Z",
  },
  {
    id: "PROP-015",
    name: "Banner Ad",
    channelId: "CHA-010",
    channelName: "R4G Website",
    mediumId: "MED-003",
    mediumName: "Web",
    description: "Banner advertisements on R4G website",
    isActive: true,
    createdAt: "2023-02-25T00:00:00Z",
  },
]

// Sample Variant data
export const sampleVariants: Variant[] = [
  {
    id: "VAR-001",
    name: "Premium Booth",
    propertyId: "PROP-001",
    propertyName: "Exhibitor",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Premium booth space with enhanced visibility",
    isActive: true,
    price: 250000,
    currency: "INR",
    benefits: ["Prime location", "Larger space", "Additional branding", "4 exhibitor passes"],
    createdAt: "2023-03-01T00:00:00Z",
  },
  {
    id: "VAR-002",
    name: "Standard Booth",
    propertyId: "PROP-001",
    propertyName: "Exhibitor",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Standard booth space",
    isActive: true,
    price: 150000,
    currency: "INR",
    benefits: ["Standard location", "Basic space", "2 exhibitor passes"],
    createdAt: "2023-03-01T00:00:00Z",
  },
  {
    id: "VAR-010",
    name: "Title Sponsor",
    propertyId: "PROP-005",
    propertyName: "Sponsor",
    channelId: "CHA-001",
    channelName: "DDX Asia",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Title sponsorship for the event",
    isActive: true,
    price: 1500000,
    currency: "INR",
    benefits: ["Title branding", "Premium booth", "Speaking slot", "Logo on all materials", "8 delegate passes"],
    createdAt: "2023-03-05T00:00:00Z",
  },
  {
    id: "VAR-015",
    name: "Multiple Categories",
    propertyId: "PROP-002",
    propertyName: "Award Entry",
    channelId: "CHA-002",
    channelName: "OAC",
    mediumId: "MED-001",
    mediumName: "Event",
    description: "Entry to multiple award categories",
    isActive: true,
    price: 35000,
    currency: "INR",
    benefits: ["Up to 3 categories", "Featured in showcase", "Certificate of participation"],
    createdAt: "2023-03-10T00:00:00Z",
  },
  {
    id: "VAR-025",
    name: "Full Page Color",
    propertyId: "PROP-010",
    propertyName: "Advertisement",
    channelId: "CHA-005",
    channelName: "OA Magazine",
    mediumId: "MED-002",
    mediumName: "Print",
    description: "Full page color advertisement",
    isActive: true,
    price: 75000,
    currency: "INR",
    benefits: ["Full page", "Color printing", "Premium placement available"],
    createdAt: "2023-03-15T00:00:00Z",
  },
  {
    id: "VAR-040",
    name: "Homepage Premium",
    propertyId: "PROP-015",
    propertyName: "Banner Ad",
    channelId: "CHA-010",
    channelName: "R4G Website",
    mediumId: "MED-003",
    mediumName: "Web",
    description: "Premium banner on website homepage",
    isActive: true,
    price: 45000,
    currency: "INR",
    benefits: ["Homepage placement", "Large format", "3-month duration"],
    createdAt: "2023-03-20T00:00:00Z",
  },
]
