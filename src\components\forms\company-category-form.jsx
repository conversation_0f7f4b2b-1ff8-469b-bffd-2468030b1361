"use client";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

export function CompanyCategoryForm({ onSubmit, onCancel, defaultValues = {} }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      status: "active",
      ...defaultValues,
    },
  });

  const status = watch("status");

  const handleFormSubmit = (data) => {
    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Company Category Name *</Label>
        <Input
          id="name"
          {...register("name", { required: "Company category name is required" })}
          placeholder="Enter company category name"
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="status"
          checked={status === "active"}
          onCheckedChange={(checked) =>
            setValue("status", checked ? "active" : "inactive")
          }
        />
        <Label htmlFor="status">Active</Label>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Save Company Category</Button>
      </div>
    </form>
  );
}
