"use client";

import { useState, useEffect } from "react";
import { Plus, MoreHorizontal, Pencil, Trash2, MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { CityForm } from "@/components/forms/city-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import cityService from "../../../services/cities.service";

export default function Cities() {
  const { toast } = useToast();
  const [cities, setCities] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCity, setSelectedCity] = useState(null);

  // Fetch cities from API using the service
  const fetchCities = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await cityService.getAllCities({ page, limit, search });

      // Ensure we have an array and handle potential data format issues
      const citiesArray = Array.isArray(result.cities) ? result.cities : [];
      setCities(citiesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch cities",
        variant: "destructive",
      });
      console.error("Error fetching cities:", error);
      setCities([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCities();
  }, []);

  const handleAddCity = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditCity = (city) => {
    setSelectedCity(city);
    setIsEditDialogOpen(true);
  };

  const handleDeleteCity = (city) => {
    setSelectedCity(city);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateCity = async (data) => {
    try {
      await cityService.createCity(data);
      await fetchCities();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "City created successfully",
      });
    } catch (error) {
      console.error("Create city error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create city",
        variant: "destructive",
      });
    }
  };

  const handleUpdateCity = async (data) => {
    try {
      await cityService.updateCity(selectedCity.id, data);
      await fetchCities();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "City updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update city",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteCity = async () => {
    try {
      await cityService.deleteCity(selectedCity.id);
      await fetchCities();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "City deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete city",
        variant: "destructive",
      });
    }
  };

  const handleImportCities = async (file) => {
    try {
      const result = await cityService.bulkImportCities(file);

      await fetchCities(); // Refresh the cities list
      toast({
        title: "Success",
        description: "Cities imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message || "Failed to import cities from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const getStatusBadge = (status) => {
    return status === "active" ? (
      <Badge className="bg-emerald-500">Active</Badge>
    ) : (
      <Badge variant="destructive">Inactive</Badge>
    );
  };

  const columns = [
    {
      accessorKey: "name",
      header: "City Name",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "state",
      header: "State",
    },
    {
      accessorKey: "country",
      header: "Country",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.getValue("status")),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const city = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditCity(city)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteCity(city)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Cities Management</CardTitle>
              <CardDescription>
                Manage cities, states, and geographical locations
              </CardDescription>
            </div>
            <Button onClick={handleAddCity}>
              <Plus className="mr-2 h-4 w-4" />
              Add City
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={cities}
            searchKey="name"
            isLoading={isLoading}
            entityName="Cities"
            onImport={handleImportCities}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/cities_template.xlsx"
            fileDescription="File should contain columns: name, state, country, status"
          />
        </CardContent>
      </Card>

      {/* Add City Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New City</DialogTitle>
            <DialogDescription>
              Create a new city record with location details.
            </DialogDescription>
          </DialogHeader>
          <CityForm
            onSubmit={handleCreateCity}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit City Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit City</DialogTitle>
            <DialogDescription>
              Update the city information and location details.
            </DialogDescription>
          </DialogHeader>
          {selectedCity && (
            <CityForm
              defaultValues={selectedCity}
              onSubmit={handleUpdateCity}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete City Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedCity?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCity}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
