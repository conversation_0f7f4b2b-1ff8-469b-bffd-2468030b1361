"use client"

import { useState } from "react"
import { But<PERSON> } from "../ui/button.jsx"
import { Label } from "../ui/label.jsx"
import { RadioGroup, RadioGroupItem } from "../ui/radio-group.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select.jsx"
import { Separator } from "../ui/separator.jsx"

export function FinancialYearSettings() {
  const [fyType, setFyType] = useState("calendar")

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Financial Year Configuration</h3>
        <p className="text-sm text-muted-foreground">Configure your organization's financial year settings.</p>
        <RadioGroup defaultValue="calendar" className="space-y-2 mt-2" onValueChange={setFyType}>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="calendar" id="calendar" />
            <Label htmlFor="calendar">Calendar Year (Jan - Dec)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="april" id="april" />
            <Label htmlFor="april">April - March</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="july" id="july" />
            <Label htmlFor="july">July - June</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="custom" id="custom" />
            <Label htmlFor="custom">Custom</Label>
          </div>
        </RadioGroup>

        {fyType === "custom" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="start-month">Start Month</Label>
              <Select defaultValue="1">
                <SelectTrigger id="start-month">
                  <SelectValue placeholder="Select start month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">January</SelectItem>
                  <SelectItem value="2">February</SelectItem>
                  <SelectItem value="3">March</SelectItem>
                  <SelectItem value="4">April</SelectItem>
                  <SelectItem value="5">May</SelectItem>
                  <SelectItem value="6">June</SelectItem>
                  <SelectItem value="7">July</SelectItem>
                  <SelectItem value="8">August</SelectItem>
                  <SelectItem value="9">September</SelectItem>
                  <SelectItem value="10">October</SelectItem>
                  <SelectItem value="11">November</SelectItem>
                  <SelectItem value="12">December</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-month">End Month</Label>
              <Select defaultValue="12">
                <SelectTrigger id="end-month">
                  <SelectValue placeholder="Select end month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">January</SelectItem>
                  <SelectItem value="2">February</SelectItem>
                  <SelectItem value="3">March</SelectItem>
                  <SelectItem value="4">April</SelectItem>
                  <SelectItem value="5">May</SelectItem>
                  <SelectItem value="6">June</SelectItem>
                  <SelectItem value="7">July</SelectItem>
                  <SelectItem value="8">August</SelectItem>
                  <SelectItem value="9">September</SelectItem>
                  <SelectItem value="10">October</SelectItem>
                  <SelectItem value="11">November</SelectItem>
                  <SelectItem value="12">December</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </div>

      <Separator />

      <div className="space-y-2">
        <h3 className="text-lg font-medium">Current Financial Year</h3>
        <p className="text-sm text-muted-foreground">Set the current financial year for reporting.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div className="space-y-2">
            <Label htmlFor="current-fy">Current Financial Year</Label>
            <Select defaultValue="2023-2024">
              <SelectTrigger id="current-fy">
                <SelectValue placeholder="Select financial year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2021-2022">2021 - 2022</SelectItem>
                <SelectItem value="2022-2023">2022 - 2023</SelectItem>
                <SelectItem value="2023-2024">2023 - 2024</SelectItem>
                <SelectItem value="2024-2025">2024 - 2025</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="fy-display">Display Format</Label>
            <Select defaultValue="fy-2023-24">
              <SelectTrigger id="fy-display">
                <SelectValue placeholder="Select display format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2023-2024">2023 - 2024</SelectItem>
                <SelectItem value="fy-2023-24">FY 2023-24</SelectItem>
                <SelectItem value="fy-23-24">FY 23-24</SelectItem>
                <SelectItem value="2023">2023</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex justify-end">
        <Button variant="outline" className="mr-2">
          Reset to Defaults
        </Button>
        <Button>Save Settings</Button>
      </div>
    </div>
  )
}
