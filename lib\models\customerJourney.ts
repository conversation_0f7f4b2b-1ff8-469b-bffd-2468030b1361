export interface CustomerJourneyStep {
  id: string
  customerId: string
  customerName: string
  step: string
  status: "pending" | "in_progress" | "completed" | "skipped"
  assignedTo?: string
  assignedToName?: string
  dueDate?: string
  completedDate?: string
  notes?: string
  createdAt: string
  updatedAt?: string
}

export interface CustomerJourney {
  id: string
  customerId: string
  customerName: string
  journeyType: "onboarding" | "sales" | "support" | "renewal"
  status: "active" | "completed" | "paused" | "cancelled"
  startDate: string
  endDate?: string
  steps: CustomerJourneyStep[]
  createdBy: string
  createdAt: string
  updatedAt?: string
}

export const sampleCustomerJourneys: CustomerJourney[] = [
  {
    id: "CJ-001",
    customerId: "CUST-001",
    customerName: "Acme Displays",
    journeyType: "sales",
    status: "completed",
    startDate: "2023-09-01",
    endDate: "2023-10-15",
    steps: [
      {
        id: "CJS-001",
        customerId: "CUST-001",
        customerName: "Acme Displays",
        step: "Initial Contact",
        status: "completed",
        assignedTo: "USER-002",
        assignedToName: "Sales Manager",
        completedDate: "2023-09-02",
        notes: "First contact made via email",
        createdAt: "2023-09-01T10:00:00Z",
      },
      {
        id: "CJS-002",
        customerId: "CUST-001",
        customerName: "Acme Displays",
        step: "Needs Assessment",
        status: "completed",
        assignedTo: "USER-003",
        assignedToName: "Sales Executive",
        completedDate: "2023-09-10",
        notes: "Identified need for premium booth space",
        createdAt: "2023-09-02T10:00:00Z",
      },
      {
        id: "CJS-003",
        customerId: "CUST-001",
        customerName: "Acme Displays",
        step: "Proposal Sent",
        status: "completed",
        assignedTo: "USER-002",
        assignedToName: "Sales Manager",
        completedDate: "2023-09-15",
        notes: "Proposal for DDX Asia premium booth sent",
        createdAt: "2023-09-10T10:00:00Z",
      },
      {
        id: "CJS-004",
        customerId: "CUST-001",
        customerName: "Acme Displays",
        step: "Contract Signed",
        status: "completed",
        assignedTo: "USER-002",
        assignedToName: "Sales Manager",
        completedDate: "2023-10-15",
        notes: "Contract signed and payment received",
        createdAt: "2023-09-15T10:00:00Z",
      },
    ],
    createdBy: "USER-002",
    createdAt: "2023-09-01T10:00:00Z",
    updatedAt: "2023-10-15T14:20:00Z",
  },
]
