import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class ProductionListService {
  async getAllProductionLists(
    params = {
      page: 1,
      limit: 100,
      search: "",
      status: "",
      vehicleType: "",
      operatorId: "",
    }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);
      if (params.vehicleType)
        queryParams.append("vehicleType", params.vehicleType);
      if (params.operatorId)
        queryParams.append("operatorId", params.operatorId);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/production-lists?${queryString}`
        : `${API_URL}/production-lists`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const productionListsData = Array.isArray(response.data)
        ? response.data
        : [];

      return {
        productionLists: productionListsData,
      };
    } catch (error) {
      console.error("Error fetching production lists:", error);
      throw error;
    }
  }

  async getProductionListById(id) {
    try {
      const response = await axios.get(`${API_URL}/production-lists/${id}`);
      const productionList = response.data;
      return productionList;
    } catch (error) {
      console.error(`Error fetching production list with ID ${id}:`, error);
      throw error;
    }
  }

  async createProductionList(productionListData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        productionSheetNo: productionListData.productionSheetNo,
        quantity: parseInt(productionListData.quantity),
        operatorId: parseInt(productionListData.operatorId),
        vehicleType: productionListData.vehicleType,
        appointmentDate: productionListData.appointmentDate,
        status: productionListData.status || "pending",
        notes: productionListData.notes || null,
      };

      const response = await axios.post(`${API_URL}/production-lists`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating production list:", error);
      throw error;
    }
  }

  async updateProductionList(id, productionListData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        productionSheetNo: productionListData.productionSheetNo,
        quantity: parseInt(productionListData.quantity),
        operatorId: parseInt(productionListData.operatorId),
        vehicleType: productionListData.vehicleType,
        appointmentDate: productionListData.appointmentDate,
        status: productionListData.status || "pending",
        notes: productionListData.notes || null,
      };

      const response = await axios.put(
        `${API_URL}/production-lists/${id}`,
        apiData
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating production list with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteProductionList(id) {
    try {
      const response = await axios.delete(`${API_URL}/production-lists/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting production list with ID ${id}:`, error);
      throw error;
    }
  }

  async changeProductionListStatus(id, status) {
    try {
      const response = await axios.patch(
        `${API_URL}/production-lists/${id}/status`,
        {
          status: status, // "pending", "in-progress", "completed", etc.
        }
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error changing production list status for ID ${id}:`,
        error
      );
      throw error;
    }
  }

  async getProductionListsByOperator(operatorId) {
    try {
      const response = await axios.get(
        `${API_URL}/production-lists/by-operator/${operatorId}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching production lists by operator ${operatorId}:`,
        error
      );
      throw error;
    }
  }

  async getProductionListsByVehicleType(vehicleType) {
    try {
      const response = await axios.get(
        `${API_URL}/production-lists/by-vehicle-type/${vehicleType}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching production lists by vehicle type ${vehicleType}:`,
        error
      );
      throw error;
    }
  }

  async searchProductionLists(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/production-lists/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(
        `Error searching production lists with term ${searchTerm}:`,
        error
      );
      throw error;
    }
  }

  async bulkImportProductionLists(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/production-lists/bulk-import`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing production lists from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const productionListService = new ProductionListService();
export default productionListService;
