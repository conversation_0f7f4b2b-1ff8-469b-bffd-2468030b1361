"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card.jsx";
import { Button } from "../../components/ui/button.jsx";
import { Input } from "../../components/ui/input.jsx";
import { Label } from "../../components/ui/label.jsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select.jsx";
import { Badge } from "../../components/ui/badge.jsx";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../components/ui/tabs.jsx";
import {
  FileText,
  Download,
  Calendar,
  Filter,
  Plus,
  Eye,
  Share,
} from "lucide-react";
import { PageHeader } from "../../components/page-header.jsx";
import { DataTable } from "../../components/data-table.jsx";
import reportsService from "../../services/reports.service.js";
import { TRANSACTION_STAGES } from "../../constants/index.js";

const reportsData = [
  {
    id: 1,
    name: "Monthly Sales Report",
    type: "Sales",
    format: "PDF",
    schedule: "Monthly",
    lastGenerated: "2024-01-15",
    status: "Completed",
    size: "2.3 MB",
  },
  {
    id: 2,
    name: "Customer Analytics",
    type: "Analytics",
    format: "Excel",
    schedule: "Weekly",
    lastGenerated: "2024-01-20",
    status: "Completed",
    size: "1.8 MB",
  },
  {
    id: 3,
    name: "Financial Summary",
    type: "Financial",
    format: "PDF",
    schedule: "Quarterly",
    lastGenerated: "2024-01-10",
    status: "Processing",
    size: "3.1 MB",
  },
  {
    id: 4,
    name: "Inventory Report",
    type: "Operations",
    format: "CSV",
    schedule: "Daily",
    lastGenerated: "2024-01-22",
    status: "Completed",
    size: "0.9 MB",
  },
  {
    id: 5,
    name: "Marketing Performance",
    type: "Marketing",
    format: "PDF",
    schedule: "Monthly",
    lastGenerated: "2024-01-18",
    status: "Failed",
    size: "1.5 MB",
  },
];

const reportTemplates = [
  {
    id: 1,
    name: "Sales Performance",
    category: "Sales",
    description: "Comprehensive sales metrics and trends",
  },
  {
    id: 2,
    name: "Customer Insights",
    category: "Analytics",
    description: "Customer behavior and demographics",
  },
  {
    id: 3,
    name: "Financial Overview",
    category: "Financial",
    description: "Revenue, expenses, and profitability",
  },
  {
    id: 4,
    name: "Marketing ROI",
    category: "Marketing",
    description: "Campaign performance and ROI analysis",
  },
  {
    id: 5,
    name: "Operational Metrics",
    category: "Operations",
    description: "Efficiency and operational KPIs",
  },
];

const columns = [
  {
    accessorKey: "name",
    header: "Report Name",
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => <Badge variant="outline">{row.getValue("type")}</Badge>,
  },
  {
    accessorKey: "format",
    header: "Format",
  },
  {
    accessorKey: "schedule",
    header: "Schedule",
  },
  {
    accessorKey: "lastGenerated",
    header: "Last Generated",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status");
      return (
        <Badge
          variant={
            status === "Completed"
              ? "default"
              : status === "Processing"
              ? "secondary"
              : "destructive"
          }
        >
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "size",
    header: "Size",
  },
];

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState(null);
  const [reportName, setReportName] = useState("");
  const [reportType, setReportType] = useState("");
  const [reportFormat, setReportFormat] = useState("");
  const [loading, setLoading] = useState(false);

  // Transaction Reports state
  const [yearFilters, setYearFilters] = useState([]);
  const [monthFilters, setMonthFilters] = useState([]);
  const [cityFilters, setCityFilters] = useState([]);
  const [channelFilters, setChannelFilters] = useState([]);
  const [mediumFilters, setMediumFilters] = useState([]);
  const [propertyFilters, setPropertyFilters] = useState([]);
  const [customerFilters, setCustomerFilters] = useState([]);
  const [stageFilters, setStageFilters] = useState([]);

  // Custom date range state
  const [isCustomDateRange, setIsCustomDateRange] = useState(false);
  const [startMonth, setStartMonth] = useState("");
  const [startYear, setStartYear] = useState("");
  const [endMonth, setEndMonth] = useState("");
  const [endYear, setEndYear] = useState("");

  // Report generation state
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [generatedReport, setGeneratedReport] = useState(null);

  // Stage Customer Report state
  const [stageCustomerFilters, setStageCustomerFilters] = useState([]);
  const [isGeneratingStageReport, setIsGeneratingStageReport] = useState(false);
  const [generatedStageReport, setGeneratedStageReport] = useState(null);

  // Filter options
  const [filterOptions, setFilterOptions] = useState({
    years: [],
    months: [],
    mediums: [],
    channels: [],
    properties: [],
    cities: [],
    customers: [],
    stages: [],
  });

  // Load filter options from API
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const options = await reportsService.getFilterOptions();
        setFilterOptions(options);
      } catch (error) {
        console.error("Error loading filter options:", error);
        // Fallback to default options if API fails
        setFilterOptions({
          years: ["2023", "2024", "2025"],
          months: [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ],
          mediums: ["Print", "Digital", "Outdoor", "Event"],
          channels: ["OA Mag", "New Media", "Events", "R4G", "M4G"],
          properties: [
            "Website",
            "Magazine",
            "Newsletter",
            "Convention",
            "Awards",
          ],
          cities: [
            "Mumbai",
            "Bengaluru",
            "Delhi",
            "Pune",
            "Chennai",
            "Hyderabad",
          ],
          customers: [
            "Xireme Media",
            "Singpost",
            "SabRentKaro",
            "JBM Group",
            "Sakal",
          ],
          stages: TRANSACTION_STAGES,
        });
      }
    };

    loadFilterOptions();
  }, []);

  const handleGenerateReport = () => {
    setLoading(true);
    // Simulate report generation
    setTimeout(() => {
      setLoading(false);
      alert("Report generated successfully!");
    }, 3000);
  };

  const handleDownloadReport = (reportId) => {
    // Simulate download
    alert(`Downloading report ${reportId}...`);
  };

  // Transaction Reports functionality
  const handleGenerateTransactionReport = async () => {
    try {
      setIsGeneratingReport(true);
      setGeneratedReport(null);

      const filters = {
        yearFilters,
        monthFilters,
        mediumFilters,
        channelFilters,
        propertyFilters,
        cityFilters,
        customerFilters,
        stageFilters,
        startMonth,
        startYear,
        endMonth,
        endYear,
        isCustomDateRange,
      };

      const requestBody = reportsService.buildFilterRequestBody(filters);
      const response = await reportsService.generateTransactionReport(
        requestBody
      );

      if (response.status) {
        setGeneratedReport(response.data);
      } else {
        alert("Failed to generate report: " + response.message);
      }
    } catch (error) {
      console.error("Error generating transaction report:", error);
      alert("Failed to generate report. Please try again.");
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const handleDownloadTransactionReport = async () => {
    if (generatedReport?.downloadUrl) {
      try {
        await reportsService.downloadReport(generatedReport.downloadUrl);
      } catch (error) {
        console.error("Error downloading report:", error);
        alert("Failed to download report. Please try again.");
      }
    }
  };

  // Stage Customer Report functionality
  const handleGenerateStageCustomerReport = async () => {
    try {
      setIsGeneratingStageReport(true);
      setGeneratedStageReport(null);

      const requestBody =
        reportsService.buildStageCustomerRequestBody(stageCustomerFilters);
      const response = await reportsService.generateStageCustomerReport(
        requestBody
      );

      if (response.status) {
        setGeneratedStageReport(response.data);
      } else {
        alert("Failed to generate stage customer report: " + response.message);
      }
    } catch (error) {
      console.error("Error generating stage customer report:", error);
      alert("Failed to generate stage customer report. Please try again.");
    } finally {
      setIsGeneratingStageReport(false);
    }
  };

  const handleDownloadStageCustomerReport = async () => {
    if (generatedStageReport?.downloadUrl) {
      try {
        await reportsService.downloadReport(generatedStageReport.downloadUrl);
      } catch (error) {
        console.error("Error downloading stage customer report:", error);
        alert("Failed to download report. Please try again.");
      }
    }
  };

  // Filter Section Component (Multi-select dropdown)
  const FilterSection = ({
    title,
    options,
    selectedValues,
    setSelectedValues,
  }) => (
    <div>
      <p className="text-sm font-medium mb-1">{title}</p>
      <div className="relative">
        <Select
          value={selectedValues.length === 0 ? "placeholder" : "selected"}
          onValueChange={(value) => {
            if (value === "all") {
              setSelectedValues([]);
            } else if (value !== "placeholder" && value !== "selected") {
              if (selectedValues.includes(value)) {
                setSelectedValues(
                  selectedValues.filter((item) => item !== value)
                );
              } else {
                setSelectedValues([...selectedValues, value]);
              }
            }
          }}
        >
          <SelectTrigger>
            <SelectValue>
              {selectedValues.length === 0
                ? `Select ${title.toLowerCase()}`
                : selectedValues.length === 1
                ? selectedValues[0]
                : `${selectedValues.length} ${title.toLowerCase()} selected`}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              <div className="flex items-center">
                <span>Clear All</span>
              </div>
            </SelectItem>
            {options.map((option) => (
              <SelectItem key={option} value={option}>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectedValues.includes(option)}
                    onChange={() => {}}
                    className="rounded"
                  />
                  <span>{option}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {selectedValues.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedValues.map((value) => (
            <Badge
              key={value}
              variant="secondary"
              className="text-xs cursor-pointer"
              onClick={() =>
                setSelectedValues(selectedValues.filter((v) => v !== value))
              }
            >
              {value} ×
            </Badge>
          ))}
        </div>
      )}
    </div>
  );

  const totalReports = reportsData.length;
  const completedReports = reportsData.filter(
    (report) => report.status === "Completed"
  ).length;
  const processingReports = reportsData.filter(
    (report) => report.status === "Processing"
  ).length;
  const failedReports = reportsData.filter(
    (report) => report.status === "Failed"
  ).length;

  return (
    <div className="space-y-6 p-6">
      <PageHeader
        title="Reports Management"
        description="Generate, schedule, and manage business reports"
      />

      

      <Tabs defaultValue="transaction-reports" className="space-y-4">
        <TabsList>
          {/* <TabsTrigger value="reports">All Reports</TabsTrigger> */}
          <TabsTrigger value="transaction-reports">
            Transaction Reports
          </TabsTrigger>
          <TabsTrigger value="stage-customer-reports">
            Stage Reports
          </TabsTrigger>
          {/* <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="create">Create Report</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger> */}
        </TabsList>

        <TabsContent value="transaction-reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction Reports</CardTitle>
              <CardDescription>
                Generate custom transaction reports with filters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Filters Section */}
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                <FilterSection
                  title="Year"
                  options={filterOptions.years}
                  selectedValues={yearFilters}
                  setSelectedValues={setYearFilters}
                />
                <FilterSection
                  title="Month"
                  options={filterOptions.months}
                  selectedValues={monthFilters}
                  setSelectedValues={setMonthFilters}
                />
                <FilterSection
                  title="City"
                  options={filterOptions.cities}
                  selectedValues={cityFilters}
                  setSelectedValues={setCityFilters}
                />
                <FilterSection
                  title="Channel"
                  options={filterOptions.channels}
                  selectedValues={channelFilters}
                  setSelectedValues={setChannelFilters}
                />
                <FilterSection
                  title="Medium"
                  options={filterOptions.mediums}
                  selectedValues={mediumFilters}
                  setSelectedValues={setMediumFilters}
                />
                <FilterSection
                  title="Property"
                  options={filterOptions.properties}
                  selectedValues={propertyFilters}
                  setSelectedValues={setPropertyFilters}
                />
                <FilterSection
                  title="Customer"
                  options={filterOptions.customers}
                  selectedValues={customerFilters}
                  setSelectedValues={setCustomerFilters}
                />
                <FilterSection
                  title="Stage"
                  options={filterOptions.stages}
                  selectedValues={stageFilters}
                  setSelectedValues={setStageFilters}
                />
              </div>

              {/* Custom Date Range Section */}
              <div className="border-t pt-4">
                <div className="flex items-center space-x-2 mb-4">
                  <input
                    type="checkbox"
                    id="custom-date-range"
                    checked={isCustomDateRange}
                    onChange={(e) => setIsCustomDateRange(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="custom-date-range">
                    Use Custom Date Range
                  </Label>
                </div>

                {isCustomDateRange && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <Label>Start Month</Label>
                      <Select value={startMonth} onValueChange={setStartMonth}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select month" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterOptions.months.map((month) => (
                            <SelectItem key={month} value={month}>
                              {month}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Start Year</Label>
                      <Select value={startYear} onValueChange={setStartYear}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select year" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterOptions.years.map((year) => (
                            <SelectItem key={year} value={year}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>End Month</Label>
                      <Select value={endMonth} onValueChange={setEndMonth}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select month" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterOptions.months.map((month) => (
                            <SelectItem key={month} value={month}>
                              {month}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>End Year</Label>
                      <Select value={endYear} onValueChange={setEndYear}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select year" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterOptions.years.map((year) => (
                            <SelectItem key={year} value={year}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>

              {/* Generate Report Button */}
              <div className="border-t pt-4">
                <Button
                  onClick={handleGenerateTransactionReport}
                  disabled={isGeneratingReport}
                  className="w-full md:w-auto"
                >
                  {isGeneratingReport ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating Report...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Transaction Report
                    </>
                  )}
                </Button>
              </div>

              {/* Generated Report Display */}
              {generatedReport && (
                <div className="border-t pt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        Report Generated Successfully
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <div className="text-muted-foreground">
                            Total Records
                          </div>
                          <div className="font-medium text-lg">
                            {generatedReport.totalRecords}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">
                            Total Revenue
                          </div>
                          <div className="font-medium text-lg">
                            ₹{generatedReport.totalRevenue?.toLocaleString()}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Status</div>
                          <Badge variant="default">Ready for Download</Badge>
                        </div>
                      </div>
                      <Button
                        onClick={handleDownloadTransactionReport}
                        className="w-full md:w-auto"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download Report
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stage-customer-reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Stage Customer Reports</CardTitle>
              <CardDescription>
                Generate customer reports filtered by stages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Stage Filter Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <FilterSection
                  title="Stages"
                  options={filterOptions.stages || []}
                  selectedValues={stageCustomerFilters}
                  setSelectedValues={setStageCustomerFilters}
                />
              </div>

              {/* Generate Report Button */}
              <div className="border-t pt-4">
                <Button
                  onClick={handleGenerateStageCustomerReport}
                  disabled={isGeneratingStageReport}
                  className="w-full md:w-auto"
                >
                  {isGeneratingStageReport ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating Stage Customer Report...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Stage Customer Report
                    </>
                  )}
                </Button>
              </div>

              {/* Generated Report Display */}
              {generatedStageReport && (
                <div className="border-t pt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        Stage Customer Report Generated Successfully
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-muted-foreground">
                            Total Records
                          </div>
                          <div className="font-medium text-lg">
                            {generatedStageReport.totalRecords}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">
                            Stages Included
                          </div>
                          <div className="font-medium">
                            {generatedStageReport.stages?.length > 0 ? (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {generatedStageReport.stages.map((stage) => (
                                  <Badge key={stage} variant="outline">
                                    {stage}
                                  </Badge>
                                ))}
                              </div>
                            ) : (
                              <Badge variant="outline">All Stages</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <Badge variant="default">Ready for Download</Badge>
                        <Button
                          onClick={handleDownloadStageCustomerReport}
                          className="w-full md:w-auto"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download Report
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Report Templates</CardTitle>
              <CardDescription>
                Pre-built report templates for quick generation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {reportTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                  >
                    <CardHeader>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription>{template.category}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">
                        {template.description}
                      </p>
                      <Button className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Use Template
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create New Report</CardTitle>
              <CardDescription>
                Configure a new report with custom parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="report-name">Report Name</Label>
                  <Input
                    id="report-name"
                    placeholder="Enter report name"
                    value={reportName}
                    onChange={(e) => setReportName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="report-type">Report Type</Label>
                  <Select value={reportType} onValueChange={setReportType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="analytics">Analytics</SelectItem>
                      <SelectItem value="financial">Financial</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="operations">Operations</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="report-format">Format</Label>
                  <Select value={reportFormat} onValueChange={setReportFormat}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date-range">Date Range</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="last-7-days">Last 7 days</SelectItem>
                      <SelectItem value="last-30-days">Last 30 days</SelectItem>
                      <SelectItem value="last-90-days">Last 90 days</SelectItem>
                      <SelectItem value="last-year">Last year</SelectItem>
                      <SelectItem value="custom">Custom range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button
                onClick={handleGenerateReport}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating Report...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Reports</CardTitle>
              <CardDescription>
                Manage automated report generation schedules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportsData
                  .filter((report) => report.schedule !== "Manual")
                  .map((report) => (
                    <div
                      key={report.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="space-y-1">
                        <div className="font-medium">{report.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {report.schedule} • {report.format} • Last:{" "}
                          {report.lastGenerated}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{report.schedule}</Badge>
                        <Button size="sm" variant="outline">
                          Edit Schedule
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
