import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class ContactStatusService {
  async getAllContactStatuses(
    params = {
      page: 1,
      limit: 100,
      search: "",
      status: "",
    }
  ) {
    try {
      const response = await axios.get(`${API_URL}/contact-statuses`, {
        params: {
          page: params.page || 1,
          limit: params.limit || 100,
          search: params.search || "",
          status: params.status || "",
        },
      });

      // Handle the specific response format where data is in response.data.data
      if (response.data && Array.isArray(response.data.data)) {
        return {
          contactStatuses: response.data.data,
          total: response.data.meta?.total || response.data.data.length,
          page: response.data.meta?.page || params.page,
          limit: response.data.meta?.limit || params.limit,
        };
      } 
      // Handle response format where contactStatuses is a field
      else if (response.data && Array.isArray(response.data.contactStatuses)) {
        return {
          contactStatuses: response.data.contactStatuses,
          total: response.data.total || response.data.contactStatuses.length,
          page: response.data.page || params.page,
          limit: response.data.limit || params.limit,
        };
      } 
      // Handle direct array response
      else if (Array.isArray(response.data)) {
        return {
          contactStatuses: response.data,
          total: response.data.length,
          page: params.page,
          limit: params.limit,
        };
      } else {
        // Fallback
        return {
          contactStatuses: [],
          total: 0,
          page: 1,
          limit: 100,
        };
      }
    } catch (error) {
      console.error("Error fetching contact statuses:", error);
      throw error;
    }
  }

  async createContactStatus(contactStatusData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: contactStatusData.name,
        status: contactStatusData.status || "active",
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/contact-statuses`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating contact status:", error);
      throw error;
    }
  }

  async updateContactStatus(id, contactStatusData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: contactStatusData.name,
        status: contactStatusData.status || "active",
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/contact-statuses/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating contact status with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteContactStatus(id) {
    try {
      const response = await axios.delete(`${API_URL}/contact-statuses/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting contact status with ID ${id}:`, error);
      throw error;
    }
  }

  async getContactStatusById(id) {
    try {
      const response = await axios.get(`${API_URL}/contact-statuses/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching contact status with ID ${id}:`, error);
      throw error;
    }
  }

  async importContactStatuses(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(`${API_URL}/contact-statuses/import`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error importing contact statuses:", error);
      throw error;
    }
  }
}

export default new ContactStatusService();
