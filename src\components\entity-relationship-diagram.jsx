"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card.jsx"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "./ui/tabs.jsx"
import mermaid from "mermaid"

function DiagramTab({ title, description, definition }) {
  return (
    <TabsContent value={title.toLowerCase().replace(/\s+/g, "")} className="mt-6">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <p className="text-muted-foreground mb-6">{description}</p>
      <EntityRelationshipDiagram definition={definition} />
    </TabsContent>
  )
}

export function EntityRelationshipDiagram({ definition }) {
  const containerRef = useRef(null)

  useEffect(() => {
    if (containerRef.current) {
      mermaid.initialize({
        startOnLoad: true,
        theme: "neutral",
        securityLevel: "loose",
        flowchart: {
          htmlLabels: true,
          curve: "basis",
        },
        er: {
          diagramPadding: 20,
        },
      })

      try {
        mermaid.render("mermaid-diagram", definition).then(({ svg }) => {
          if (containerRef.current) {
            containerRef.current.innerHTML = svg
          }
        })
      } catch (error) {
        console.error("Error rendering mermaid diagram:", error)
      }
    }
  }, [definition, containerRef])

  return <div ref={containerRef} className="overflow-auto bg-white p-4 rounded-md border" />
}

export default function EntityRelationshipDiagramWrapper() {
  const businessHierarchyDefinition = `
  graph TD
    Medium["Medium (Event, Print, Web)"]-->Channel["Channel (DDX Asia, OAC, Talks OAH)"]
    Channel-->Property["Property (Exhibitor, Award Entries, etc.)"]
    Property-->Variant["Variant (Title Sponsor, Associate Sponsor, etc.)"]
    
    class Medium,Channel,Property,Variant diagramNode;
    classDef diagramNode fill:#f4f4f4,stroke:#333,stroke-width:1px;
`

  const dataModelDefinition = `
  graph TD
    Customer["Customer Company"]-->Employees["Customer Employees"]
    Customer-->Transactions["Transactions"]
    Transactions-->Medium["Medium"]
    Transactions-->Channel["Channel"] 
    Transactions-->Property["Property"]
    Transactions-->Variant["Variant"]
    Users["Users (Staff)"]-->Transactions
    Transactions-->Reports["Reports & Insights"]
    
    class Customer,Employees,Transactions,Medium,Channel,Property,Variant,Users,Reports diagramNode;
    classDef diagramNode fill:#f4f4f4,stroke:#333,stroke-width:1px;
`

  const transactionFlowDefinition = `
  graph LR
    Sales["Sales Team"]-->|Creates|Transaction["Transaction"]
    Customer["Customer"]-->|Purchases|Transaction
    Transaction-->|Categorized by|Medium["Medium"]
    Transaction-->|Associated with|Channel["Channel"]
    Transaction-->|For specific|Property["Property"]
    Transaction-->|With|Variant["Variant"]
    Transaction-->|Generates|Reports["Reports & Analytics"]
    Reports-->|Viewed by|Stakeholders["Different User Roles"]
    
    class Sales,Customer,Transaction,Medium,Channel,Property,Variant,Reports,Stakeholders diagramNode;
    classDef diagramNode fill:#f4f4f4,stroke:#333,stroke-width:1px;
`

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>R4G Business Intelligence System Structure</CardTitle>
        <CardDescription>
          Visual representation of the R4G BI system structure, data relationships, and transaction flow
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="hierarchy" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="hierarchy">Business Hierarchy</TabsTrigger>
            <TabsTrigger value="datamodel">Data Model</TabsTrigger>
            <TabsTrigger value="transactionflow">Transaction Flow</TabsTrigger>
          </TabsList>
          <DiagramTab
            title="Business Hierarchy"
            description="The hierarchical structure of R4G's business entities, showing the relationship from Medium down to Variant."
            definition={businessHierarchyDefinition}
          />
          <DiagramTab
            title="Data Model"
            description="How different entities in the R4G BI system relate to each other, showing the connections between customers, transactions, and organizational structure."
            definition={dataModelDefinition}
          />
          <DiagramTab
            title="Transaction Flow"
            description="The flow of transactions in the system, from creation by sales team to reporting and analytics for stakeholders."
            definition={transactionFlowDefinition}
          />
        </Tabs>
      </CardContent>
    </Card>
  )
}
