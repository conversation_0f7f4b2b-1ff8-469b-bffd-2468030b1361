import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class CustomerService {
  async getAllCustomers(
    params = { page: 1, limit: 100, search: "", status: "", industry: "" }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);
      if (params.industry) queryParams.append("industry", params.industry);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/customers?${queryString}`
        : `${API_URL}/customers`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const customersData = Array.isArray(response.data) ? response.data : [];

      return {
        customers: customersData,
      };
    } catch (error) {
      console.error("Error fetching customers:", error);
      throw error;
    }
  }

  async getCustomerById(id) {
    try {
      const response = await axios.get(`${API_URL}/customers/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching customer with ID ${id}:`, error);
      throw error;
    }
  }

  async createCustomer(customerData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: customerData.name,
        email: customerData.email || null,
        phone: customerData.phone || null,
        address: customerData.address || null,
        cityId: customerData.cityId || null,
        state: customerData.state || null,
        country: customerData.country || "India",
        pincode: customerData.pincode || null,
        website: customerData.website || null,
        industry: customerData.industry || null,
        companyCategoryId: customerData.companyCategoryId || null,
        gstNumber: customerData.gstNumber || null,
        panNumber: customerData.panNumber || null,
        logo: customerData.logo || null,
        status: customerData.status || "active",
        notes: customerData.notes || null,
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/customers`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating customer:", error);
      throw error;
    }
  }

  async updateCustomer(id, customerData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: customerData.name,
        email: customerData.email || null,
        phone: customerData.phone || null,
        address: customerData.address || null,
        cityId: customerData.cityId || null,
        state: customerData.state || null,
        country: customerData.country || "India",
        pincode: customerData.pincode || null,
        website: customerData.website || null,
        industry: customerData.industry || null,
        companyCategoryId: customerData.companyCategoryId || null,
        gstNumber: customerData.gstNumber || null,
        panNumber: customerData.panNumber || null,
        logo: customerData.logo || null,
        status: customerData.status || "active",
        notes: customerData.notes || null,
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/customers/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating customer with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteCustomer(id) {
    try {
      const response = await axios.delete(`${API_URL}/customers/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting customer with ID ${id}:`, error);
      throw error;
    }
  }

  async changeCustomerStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/customers/${id}/status`, {
        status: status,
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing customer status for ID ${id}:`, error);
      throw error;
    }
  }

  // Helper method to get cities for dropdown
  async getCities() {
    try {
      const response = await axios.get(`${API_URL}/cities`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching cities:", error);
      return [];
    }
  }

  // Helper method to get industries for dropdown
  async getIndustries() {
    try {
      const response = await axios.get(`${API_URL}/industries`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching industries:", error);
      return [
        "Technology",
        "Healthcare",
        "Finance",
        "Education",
        "Manufacturing",
        "Retail",
        "Real Estate",
        "Consulting",
        "Media & Entertainment",
        "Other",
      ];
    }
  }

  async bulkImportCustomers(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/customers/insert-customers`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing customers from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const customerService = new CustomerService();
export default customerService;
