import { API_CONFIG } from "../config/api.js"
import { HTTP_METHODS } from "../types/api.js"

class ApiClient {
  constructor(baseUrl = API_CONFIG.BASE_URL) {
    this.baseUrl = baseUrl
    this.timeout = API_CONFIG.TIMEOUT
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`

    const config = {
      method: options.method || HTTP_METHODS.GET,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      credentials: "include", // Include cookies
      signal: AbortSignal.timeout(this.timeout),
      ...options,
    }

    if (options.body && config.method !== HTTP_METHODS.GET) {
      config.body = JSON.stringify(options.body)
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
          // Redirect to login if unauthorized
          if (typeof window !== "undefined") {
            window.location.href = "/login"
          }
        }
        throw new Error(data.error || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error("API request failed:", error)
      throw error
    }
  }

  // Generic CRUD methods
  async get(endpoint, params = {}) {
    const searchParams = Object.keys(params).length ? `?${new URLSearchParams(params).toString()}` : ""
    return this.request(`${endpoint}${searchParams}`)
  }

  async post(endpoint, data) {
    return this.request(endpoint, {
      method: HTTP_METHODS.POST,
      body: data,
    })
  }

  async put(endpoint, data) {
    return this.request(endpoint, {
      method: HTTP_METHODS.PUT,
      body: data,
    })
  }

  async patch(endpoint, data) {
    return this.request(endpoint, {
      method: HTTP_METHODS.PATCH,
      body: data,
    })
  }

  async delete(endpoint) {
    return this.request(endpoint, {
      method: HTTP_METHODS.DELETE,
    })
  }

  // Auth methods
  auth = {
    login: (email, password) => this.post("/auth/login", { email, password }),
    logout: () => this.post("/auth/logout", {}),
    register: (userData) => this.post("/auth/register", userData),
    me: () => this.get("/auth/me"),
    refresh: () => this.post("/auth/refresh", {}),
  }

  // Customer methods
  customers = {
    getAll: (params = {}) => this.get("/customers", params),
    getById: (id) => this.get(`/customers/${id}`),
    create: (data) => this.post("/customers", data),
    update: (id, data) => this.put(`/customers/${id}`, data),
    delete: (id) => this.delete(`/customers/${id}`),
    search: (query) => this.get("/customers/search", { q: query }),
  }

  // Transaction methods
  transactions = {
    getAll: (params = {}) => this.get("/transactions", params),
    getById: (id) => this.get(`/transactions/${id}`),
    create: (data) => this.post("/transactions", data),
    update: (id, data) => this.put(`/transactions/${id}`, data),
    delete: (id) => this.delete(`/transactions/${id}`),
    getByCustomer: (customerId) => this.get(`/transactions/by-customer/${customerId}`),
  }

  // User methods
  users = {
    getAll: (params = {}) => this.get("/users", params),
    getById: (id) => this.get(`/users/${id}`),
    create: (data) => this.post("/users", data),
    update: (id, data) => this.put(`/users/${id}`, data),
    delete: (id) => this.delete(`/users/${id}`),
  }

  // Master data methods
  masters = {
    mediums: {
      getAll: (params = {}) => this.get("/masters/mediums", params),
      create: (data) => this.post("/masters/mediums", data),
      update: (id, data) => this.put(`/masters/mediums/${id}`, data),
      delete: (id) => this.delete(`/masters/mediums/${id}`),
    },
    channels: {
      getAll: (params = {}) => this.get("/masters/channels", params),
      create: (data) => this.post("/masters/channels", data),
      update: (id, data) => this.put(`/masters/channels/${id}`, data),
      delete: (id) => this.delete(`/masters/channels/${id}`),
    },
    properties: {
      getAll: (params = {}) => this.get("/masters/properties", params),
      create: (data) => this.post("/masters/properties", data),
      update: (id, data) => this.put(`/masters/properties/${id}`, data),
      delete: (id) => this.delete(`/masters/properties/${id}`),
    },
    variants: {
      getAll: (params = {}) => this.get("/masters/variants", params),
      create: (data) => this.post("/masters/variants", data),
      update: (id, data) => this.put(`/masters/variants/${id}`, data),
      delete: (id) => this.delete(`/masters/variants/${id}`),
    },
    cities: {
      getAll: (params = {}) => this.get("/masters/cities", params),
      create: (data) => this.post("/masters/cities", data),
      update: (id, data) => this.put(`/masters/cities/${id}`, data),
      delete: (id) => this.delete(`/masters/cities/${id}`),
    },
    companies: {
      getAll: (params = {}) => this.get("/masters/companies", params),
      create: (data) => this.post("/masters/companies", data),
      update: (id, data) => this.put(`/masters/companies/${id}`, data),
      delete: (id) => this.delete(`/masters/companies/${id}`),
    },
  }

  // Customer Journey methods
  customerJourney = {
    getAll: (params = {}) => this.get("/customer-journey", params),
    getById: (id) => this.get(`/customer-journey/${id}`),
    create: (data) => this.post("/customer-journey", data),
    update: (id, data) => this.put(`/customer-journey/${id}`, data),
    delete: (id) => this.delete(`/customer-journey/${id}`),
    bulkUpload: (data) => this.post("/customer-journey/bulk-upload", data),
  }

  // Analytics methods
  analytics = {
    getDashboard: () => this.get("/analytics/dashboard"),
    getRevenue: (params = {}) => this.get("/analytics/revenue", params),
    getCustomerJourney: (params = {}) => this.get("/analytics/customer-journey", params),
    getDemographics: (params = {}) => this.get("/analytics/demographics", params),
    getEvents: (params = {}) => this.get("/analytics/events", params),
  }

  // Reports methods
  reports = {
    generate: (reportType, params = {}) => this.post("/reports/generate", { reportType, ...params }),
    download: (reportId) => this.get(`/reports/download/${reportId}`),
    list: (params = {}) => this.get("/reports/list", params),
  }
}

// Create and export API client instance
export const api = new ApiClient()
export default api
