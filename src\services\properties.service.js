import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class PropertiesService {
  async getAllProperties(
    params = { page: 1, limit: 100, search: "", status: "" }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/properties?${queryString}`
        : `${API_URL}/properties`;

      const response = await axios.get(url);

      // Ensure data is in expected format and transform status to isActive
      const propertiesData = Array.isArray(response.data) ? response.data : [];
      const transformedData = propertiesData.map((item) => ({
        ...item,
        isActive: item.status === "active", // Convert status enum to boolean for frontend
      }));

      return {
        properties: transformedData,
      };
    } catch (error) {
      console.error("Error fetching properties:", error);
      throw error;
    }
  }

  async getPropertyById(id) {
    try {
      const response = await axios.get(`${API_URL}/properties/${id}`);
      const property = response.data;

      // Transform status to isActive for frontend compatibility
      return {
        ...property,
        isActive: property.status === "active",
      };
    } catch (error) {
      console.error(`Error fetching property with ID ${id}:`, error);
      throw error;
    }
  }

  async createProperty(propertyData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: propertyData.name,
        description: propertyData.description || "",
        channelId: propertyData.channelId,
        mediumId: propertyData.mediumId,
        price: propertyData.price || null,
        currency: propertyData.currency || "INR",
        status: propertyData.isActive ? "active" : "inactive", // Convert boolean to enum
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/properties`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating property:", error);
      throw error;
    }
  }

  async updateProperty(id, propertyData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: propertyData.name,
        description: propertyData.description || "",
        channelId: propertyData.channelId,
        mediumId: propertyData.mediumId,
        price: propertyData.price || null,
        currency: propertyData.currency || "INR",
        status: propertyData.isActive ? "active" : "inactive", // Convert boolean to enum
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/properties/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating property with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteProperty(id) {
    try {
      const response = await axios.delete(`${API_URL}/properties/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting property with ID ${id}:`, error);
      throw error;
    }
  }

  // Helper method to get channels for dropdown
  async getChannels() {
    try {
      const response = await axios.get(`${API_URL}/channels`);
      const channelsData = Array.isArray(response.data) ? response.data : [];
      return channelsData.filter(
        (channel) => channel.status === "active" || channel.isActive
      );
    } catch (error) {
      console.error("Error fetching channels:", error);
      return [];
    }
  }

  // Helper method to get mediums for dropdown
  async getMediums() {
    try {
      const response = await axios.get(`${API_URL}/mediums`);
      const mediumsData = Array.isArray(response.data) ? response.data : [];
      return mediumsData.filter(
        (medium) => medium.status === "active" || medium.isActive
      );
    } catch (error) {
      console.error("Error fetching mediums:", error);
      return [];
    }
  }

  async bulkImportProperties(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/properties/insert-properties`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing properties from Excel:", error);
      throw error;
    }
  }
}

// Export a singleton instance
const propertiesService = new PropertiesService();
export default propertiesService;
