"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "../ui/button.jsx"
import { Input } from "../ui/input.jsx"
import { Label } from "../ui/label.jsx"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table.jsx"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select.jsx"
import { Checkbox } from "../ui/checkbox.jsx"
import { PlusCircle, Pencil, Trash2, UserPlus } from "lucide-react"

// Mock data for users
const mockUsers = [
  { id: 1, name: "Admin User", email: "<EMAIL>", role: "Administrator", status: "Active" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", role: "Editor", status: "Active" },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", role: "Viewer", status: "Active" },
  { id: 4, name: "<PERSON>", email: "<EMAIL>", role: "Editor", status: "Inactive" },
]

// Mock data for roles
const mockRoles = [
  { id: 1, name: "Administrator", description: "Full access to all features" },
  { id: 2, name: "Editor", description: "Can edit content but not system settings" },
  { id: 3, name: "Viewer", description: "Read-only access to dashboards and reports" },
]

// Mock permissions
const mockPermissions = [
  { id: 1, name: "View Dashboard", category: "Dashboard" },
  { id: 2, name: "Edit Dashboard", category: "Dashboard" },
  { id: 3, name: "View Analytics", category: "Analytics" },
  { id: 4, name: "Export Reports", category: "Reports" },
  { id: 5, name: "Manage Users", category: "Administration" },
  { id: 6, name: "Manage Settings", category: "Administration" },
  { id: 7, name: "Manage Masters", category: "Data" },
  { id: 8, name: "Import/Export Data", category: "Data" },
]

export function UserRoleManagement() {
  const [activeTab, setActiveTab] = useState("users")
  const [isAddUserOpen, setIsAddUserOpen] = useState(false)
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex space-x-4 border-b">
        <button
          className={`pb-2 pt-1 ${
            activeTab === "users" ? "border-b-2 border-primary font-medium" : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("users")}
        >
          Users
        </button>
        <button
          className={`pb-2 pt-1 ${
            activeTab === "roles" ? "border-b-2 border-primary font-medium" : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("roles")}
        >
          Roles & Permissions
        </button>
      </div>

      {activeTab === "users" && (
        <div className="space-y-4">
          <div className="flex justify-between">
            <h3 className="text-lg font-medium">User Management</h3>
            <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Invite User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Invite New User</DialogTitle>
                  <DialogDescription>Send an invitation to a new user to join the system.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" placeholder="Enter user's name" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="Enter user's email" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockRoles.map((role) => (
                          <SelectItem key={role.id} value={role.name}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddUserOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsAddUserOpen(false)}>Send Invitation</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        user.status === "Active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}
                    >
                      {user.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon">
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {activeTab === "roles" && (
        <div className="space-y-4">
          <div className="flex justify-between">
            <h3 className="text-lg font-medium">Roles & Permissions</h3>
            <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
              <DialogTrigger asChild>
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Role
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Role</DialogTitle>
                  <DialogDescription>Define a new role with specific permissions.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="role-name">Role Name</Label>
                    <Input id="role-name" placeholder="Enter role name" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role-description">Description</Label>
                    <Input id="role-description" placeholder="Enter role description" />
                  </div>
                  <div className="space-y-2">
                    <Label>Permissions</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[300px] overflow-y-auto p-2">
                      {mockPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2">
                          <Checkbox id={`permission-${permission.id}`} />
                          <Label htmlFor={`permission-${permission.id}`} className="text-sm">
                            {permission.name}
                            <span className="text-xs text-muted-foreground ml-1">({permission.category})</span>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddRoleOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsAddRoleOpen(false)}>Create Role</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Role Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Users</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockRoles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">{role.name}</TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>{mockUsers.filter((user) => user.role === role.name).length}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon">
                      <Pencil className="h-4 w-4" />
                    </Button>
                    {role.name !== "Administrator" && (
                      <Button variant="ghost" size="icon">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
