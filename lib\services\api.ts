import { API_CONFIG, API_ENDPOINTS } from "@/lib/config/api"

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface ApiRequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE"
  headers?: Record<string, string>
  body?: any
  params?: Record<string, string>
  fallback?: any
}

class ApiService {
  private baseUrl: string
  private timeout: number
  private isDevelopment: boolean

  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
    this.isDevelopment = process.env.NODE_ENV === "development"
  }

  private async request<T>(endpoint: string, options: ApiRequestOptions = {}): Promise<ApiResponse<T>> {
    const { method = "GET", headers = {}, body, params, fallback } = options

    // Build URL with query parameters
    const url = new URL(`${this.baseUrl}${endpoint}`)
    if (params && method === "GET") {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value)
      })
    }

    // Prepare request configuration
    const config: RequestInit = {
      method,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      credentials: "include", // Include cookies for authentication
    }

    // Add body for non-GET requests
    if (body && method !== "GET") {
      config.body = JSON.stringify(body)
    }

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)

      const response = await fetch(url.toString(), {
        ...config,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      const data = await response.json()

      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
          // Redirect to login if unauthorized
          if (typeof window !== "undefined") {
            window.location.href = "/login"
          }
        }
        throw new Error(data.error || data.message || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error: any) {
      console.error("API request failed:", error)

      // In development, return fallback data if available
      if (this.isDevelopment && fallback) {
        console.warn(`Using fallback data for ${endpoint} due to error:`, error.message)
        return {
          success: true,
          data: fallback,
          message: "Using fallback data (API error)",
        }
      }

      throw error
    }
  }

  // Authentication methods
  auth = {
    login: (email: string, password: string) =>
      this.request(API_ENDPOINTS.AUTH.LOGIN, {
        method: "POST",
        body: { email, password },
        fallback: {
          user: {
            id: "1",
            name: "Admin User",
            email: "<EMAIL>",
            role: "admin",
            department: "IT",
            isActive: true,
          },
          token: "mock-jwt-token",
        },
      }),

    logout: () =>
      this.request(API_ENDPOINTS.AUTH.LOGOUT, {
        method: "POST",
        fallback: { success: true },
      }),

    register: (userData: any) =>
      this.request(API_ENDPOINTS.AUTH.REGISTER, {
        method: "POST",
        body: userData,
      }),

    me: () =>
      this.request(API_ENDPOINTS.AUTH.ME, {
        fallback: {
          id: "1",
          name: "Admin User",
          email: "<EMAIL>",
          role: "admin",
          department: "IT",
          isActive: true,
        },
      }),

    refresh: () =>
      this.request(API_ENDPOINTS.AUTH.REFRESH, {
        method: "POST",
        fallback: { success: true },
      }),
  }

  // User methods
  users = {
    getAll: (params?: Record<string, string>) =>
      this.request(API_ENDPOINTS.USERS.ALL, {
        method: "POST",
        body: params || {},
        fallback: {
          users: [
            {
              id: "1",
              name: "John Smith",
              email: "<EMAIL>",
              role: "admin",
              department: "Management",
              status: "active",
              lastActive: "2023-04-29T10:30:00",
            },
            {
              id: "2",
              name: "Sarah Johnson",
              email: "<EMAIL>",
              role: "editor",
              department: "Content",
              status: "active",
              lastActive: "2023-04-29T09:15:00",
            },
          ],
          pagination: { page: 1, limit: 10, total: 2, totalPages: 1 },
        },
      }),

    create: (userData: any) =>
      this.request(API_ENDPOINTS.USERS.CREATE, {
        method: "POST",
        body: userData,
        fallback: { id: Date.now().toString(), ...userData },
      }),

    update: (id: string, userData: any) =>
      this.request(API_ENDPOINTS.USERS.UPDATE, {
        method: "POST",
        body: { id, ...userData },
        fallback: { id, ...userData },
      }),

    delete: (id: string) =>
      this.request(API_ENDPOINTS.USERS.DELETE, {
        method: "POST",
        body: { id },
        fallback: { success: true },
      }),

    getById: (id: string) =>
      this.request(API_ENDPOINTS.USERS.BY_ID, {
        method: "POST",
        body: { id },
        fallback: {
          id,
          name: "Sample User",
          email: "<EMAIL>",
          role: "viewer",
          department: "General",
          status: "active",
        },
      }),
  }

  // Customer methods
  customers = {
    getAll: (params?: Record<string, string>) =>
      this.request(API_ENDPOINTS.CUSTOMERS.ALL, {
        method: "POST",
        body: params || {},
        fallback: {
          customers: [
            {
              id: "1",
              customerCode: "CUST001",
              name: "Tech Solutions Ltd",
              email: "<EMAIL>",
              phone: "+1234567890",
              address: "123 Business St, City",
              city: "New York",
              state: "NY",
              country: "USA",
              pincode: "10001",
              type: "Corporate",
              industry: "Technology",
              status: "active",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
        },
      }),

    create: (customerData: any) =>
      this.request(API_ENDPOINTS.CUSTOMERS.CREATE, {
        method: "POST",
        body: customerData,
        fallback: {
          id: Date.now().toString(),
          customerCode: `CUST${Date.now()}`,
          ...customerData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      }),

    update: (id: string, customerData: any) =>
      this.request(API_ENDPOINTS.CUSTOMERS.UPDATE, {
        method: "POST",
        body: { id, ...customerData },
        fallback: {
          id,
          ...customerData,
          updatedAt: new Date().toISOString(),
        },
      }),

    delete: (id: string) =>
      this.request(API_ENDPOINTS.CUSTOMERS.DELETE, {
        method: "POST",
        body: { id },
        fallback: { success: true, message: "Customer deleted successfully" },
      }),

    getById: (id: string) =>
      this.request(API_ENDPOINTS.CUSTOMERS.BY_ID, {
        method: "POST",
        body: { id },
        fallback: {
          id,
          customerCode: "CUST001",
          name: "Sample Company",
          email: "<EMAIL>",
          phone: "+1234567890",
          address: "123 Sample St",
          city: "Sample City",
          state: "SC",
          country: "USA",
          pincode: "12345",
          type: "Corporate",
          industry: "Technology",
          status: "active",
        },
      }),

    search: (query: string, filters?: any) =>
      this.request(API_ENDPOINTS.CUSTOMERS.SEARCH, {
        method: "POST",
        body: { query, ...filters },
        fallback: {
          customers: [],
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
        },
      }),
  }

  // Transaction methods
  transactions = {
    getAll: (params?: Record<string, string>) =>
      this.request(API_ENDPOINTS.TRANSACTIONS.ALL, {
        method: "POST",
        body: params || {},
        fallback: {
          transactions: [
            {
              id: "1",
              transactionCode: "TXN001",
              customerId: "1",
              customerName: "Tech Solutions Ltd",
              amount: 25000,
              currency: "INR",
              date: new Date().toISOString(),
              status: "completed",
              paymentStatus: "paid",
              description: "Website Development Project",
              mediumName: "Digital",
              channelName: "Website",
              propertyName: "Corporate Website",
              variantName: "Premium Package",
            },
          ],
          pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
        },
      }),

    create: (transactionData: any) =>
      this.request(API_ENDPOINTS.TRANSACTIONS.CREATE, {
        method: "POST",
        body: transactionData,
        fallback: {
          id: Date.now().toString(),
          transactionCode: `TXN${Date.now()}`,
          ...transactionData,
          createdAt: new Date().toISOString(),
        },
      }),

    update: (id: string, transactionData: any) =>
      this.request(API_ENDPOINTS.TRANSACTIONS.UPDATE, {
        method: "POST",
        body: { id, ...transactionData },
        fallback: { id, ...transactionData },
      }),

    delete: (id: string) =>
      this.request(API_ENDPOINTS.TRANSACTIONS.DELETE, {
        method: "POST",
        body: { id },
        fallback: { success: true },
      }),

    getById: (id: string) =>
      this.request(API_ENDPOINTS.TRANSACTIONS.BY_ID, {
        method: "POST",
        body: { id },
        fallback: {
          id,
          transactionCode: "TXN001",
          customerId: "1",
          amount: 25000,
          currency: "INR",
          status: "completed",
        },
      }),

    getByCustomer: (customerId: string) =>
      this.request(API_ENDPOINTS.TRANSACTIONS.BY_CUSTOMER, {
        method: "POST",
        body: { customerId },
        fallback: { transactions: [] },
      }),
  }

  // Master data methods
  masters = {
    mediums: {
      getAll: (params?: Record<string, string>) =>
        this.request(API_ENDPOINTS.MASTERS.MEDIUMS.ALL, {
          method: "POST",
          body: params || {},
          fallback: {
            mediums: [
              { id: "1", name: "Digital", description: "Online media", isActive: true },
              { id: "2", name: "Print", description: "Traditional print media", isActive: true },
              { id: "3", name: "Event", description: "Physical events", isActive: true },
            ],
          },
        }),

      create: (data: any) =>
        this.request(API_ENDPOINTS.MASTERS.MEDIUMS.CREATE, {
          method: "POST",
          body: data,
          fallback: { id: Date.now().toString(), ...data },
        }),

      update: (id: string, data: any) =>
        this.request(API_ENDPOINTS.MASTERS.MEDIUMS.UPDATE, {
          method: "POST",
          body: { id, ...data },
          fallback: { id, ...data },
        }),

      delete: (id: string) =>
        this.request(API_ENDPOINTS.MASTERS.MEDIUMS.DELETE, {
          method: "POST",
          body: { id },
          fallback: { success: true },
        }),
    },

    channels: {
      getAll: (params?: Record<string, string>) =>
        this.request(API_ENDPOINTS.MASTERS.CHANNELS.ALL, {
          method: "POST",
          body: params || {},
          fallback: {
            channels: [
              { id: "1", name: "Facebook", mediumId: "1", mediumName: "Digital", isActive: true },
              { id: "2", name: "Google Ads", mediumId: "1", mediumName: "Digital", isActive: true },
              { id: "3", name: "Newspaper", mediumId: "2", mediumName: "Print", isActive: true },
            ],
          },
        }),

      create: (data: any) =>
        this.request(API_ENDPOINTS.MASTERS.CHANNELS.CREATE, {
          method: "POST",
          body: data,
          fallback: { id: Date.now().toString(), ...data },
        }),

      update: (id: string, data: any) =>
        this.request(API_ENDPOINTS.MASTERS.CHANNELS.UPDATE, {
          method: "POST",
          body: { id, ...data },
          fallback: { id, ...data },
        }),

      delete: (id: string) =>
        this.request(API_ENDPOINTS.MASTERS.CHANNELS.DELETE, {
          method: "POST",
          body: { id },
          fallback: { success: true },
        }),
    },

    properties: {
      getAll: (params?: Record<string, string>) =>
        this.request(API_ENDPOINTS.MASTERS.PROPERTIES.ALL, {
          method: "POST",
          body: params || {},
          fallback: {
            properties: [
              {
                id: "1",
                name: "Corporate Website",
                channelId: "1",
                channelName: "Facebook",
                mediumName: "Digital",
                isActive: true,
              },
            ],
          },
        }),

      create: (data: any) =>
        this.request(API_ENDPOINTS.MASTERS.PROPERTIES.CREATE, {
          method: "POST",
          body: data,
          fallback: { id: Date.now().toString(), ...data },
        }),

      update: (id: string, data: any) =>
        this.request(API_ENDPOINTS.MASTERS.PROPERTIES.UPDATE, {
          method: "POST",
          body: { id, ...data },
          fallback: { id, ...data },
        }),

      delete: (id: string) =>
        this.request(API_ENDPOINTS.MASTERS.PROPERTIES.DELETE, {
          method: "POST",
          body: { id },
          fallback: { success: true },
        }),
    },

    variants: {
      getAll: (params?: Record<string, string>) =>
        this.request(API_ENDPOINTS.MASTERS.VARIANTS.ALL, {
          method: "POST",
          body: params || {},
          fallback: {
            variants: [
              {
                id: "1",
                name: "Premium Package",
                propertyId: "1",
                propertyName: "Corporate Website",
                channelName: "Facebook",
                mediumName: "Digital",
                price: 50000,
                currency: "INR",
                isActive: true,
              },
            ],
          },
        }),

      create: (data: any) =>
        this.request(API_ENDPOINTS.MASTERS.VARIANTS.CREATE, {
          method: "POST",
          body: data,
          fallback: { id: Date.now().toString(), ...data },
        }),

      update: (id: string, data: any) =>
        this.request(API_ENDPOINTS.MASTERS.VARIANTS.UPDATE, {
          method: "POST",
          body: { id, ...data },
          fallback: { id, ...data },
        }),

      delete: (id: string) =>
        this.request(API_ENDPOINTS.MASTERS.VARIANTS.DELETE, {
          method: "POST",
          body: { id },
          fallback: { success: true },
        }),
    },

    cities: {
      getAll: (params?: Record<string, string>) =>
        this.request(API_ENDPOINTS.MASTERS.CITIES.ALL, {
          method: "POST",
          body: params || {},
          fallback: {
            cities: [
              { id: "1", name: "Mumbai", state: "Maharashtra", country: "India", isActive: true },
              { id: "2", name: "Delhi", state: "Delhi", country: "India", isActive: true },
            ],
          },
        }),

      create: (data: any) =>
        this.request(API_ENDPOINTS.MASTERS.CITIES.CREATE, {
          method: "POST",
          body: data,
          fallback: { id: Date.now().toString(), ...data },
        }),

      update: (id: string, data: any) =>
        this.request(API_ENDPOINTS.MASTERS.CITIES.UPDATE, {
          method: "POST",
          body: { id, ...data },
          fallback: { id, ...data },
        }),

      delete: (id: string) =>
        this.request(API_ENDPOINTS.MASTERS.CITIES.DELETE, {
          method: "POST",
          body: { id },
          fallback: { success: true },
        }),
    },
  }

  // Analytics methods
  analytics = {
    getDashboard: (params?: any) =>
      this.request(API_ENDPOINTS.ANALYTICS.DASHBOARD, {
        method: "POST",
        body: params || {},
        fallback: {
          totalCustomers: 150,
          totalRevenue: 2500000,
          totalTransactions: 1250,
          activeProjects: 45,
          revenueGrowth: 12.5,
          customerGrowth: 8.3,
          transactionGrowth: 15.2,
          projectGrowth: 6.7,
          monthlyRevenue: [
            { month: "Jan", revenue: 180000 },
            { month: "Feb", revenue: 220000 },
            { month: "Mar", revenue: 195000 },
            { month: "Apr", revenue: 240000 },
            { month: "May", revenue: 280000 },
            { month: "Jun", revenue: 320000 },
          ],
          topCustomers: [
            { name: "Tech Solutions Ltd", revenue: 450000 },
            { name: "Digital Marketing Pro", revenue: 380000 },
            { name: "Creative Agency", revenue: 320000 },
          ],
          recentTransactions: [
            {
              id: "1",
              customer: "Tech Solutions Ltd",
              amount: 25000,
              date: new Date().toISOString(),
              status: "completed",
            },
          ],
        },
      }),

    getRevenue: (params?: any) =>
      this.request(API_ENDPOINTS.ANALYTICS.REVENUE, {
        method: "POST",
        body: params || {},
        fallback: {
          totalRevenue: 2500000,
          monthlyRevenue: [
            { month: "Jan", revenue: 180000 },
            { month: "Feb", revenue: 220000 },
            { month: "Mar", revenue: 195000 },
            { month: "Apr", revenue: 240000 },
            { month: "May", revenue: 280000 },
            { month: "Jun", revenue: 320000 },
          ],
        },
      }),

    getCustomerJourney: (params?: any) =>
      this.request(API_ENDPOINTS.ANALYTICS.CUSTOMER_JOURNEY, {
        method: "POST",
        body: params || {},
        fallback: {
          stages: [
            { stage: "Awareness", count: 1000 },
            { stage: "Interest", count: 750 },
            { stage: "Consideration", count: 500 },
            { stage: "Purchase", count: 200 },
            { stage: "Retention", count: 150 },
          ],
        },
      }),

    getDemographics: (params?: any) =>
      this.request(API_ENDPOINTS.ANALYTICS.DEMOGRAPHICS, {
        method: "POST",
        body: params || {},
        fallback: {
          ageGroups: [
            { group: "18-25", count: 120 },
            { group: "26-35", count: 280 },
            { group: "36-45", count: 350 },
            { group: "46-55", count: 200 },
            { group: "55+", count: 150 },
          ],
          locations: [
            { city: "New York", count: 45 },
            { city: "Los Angeles", count: 38 },
            { city: "Chicago", count: 32 },
            { city: "Houston", count: 28 },
            { city: "Phoenix", count: 22 },
          ],
        },
      }),

    getEvents: (params?: any) =>
      this.request(API_ENDPOINTS.ANALYTICS.EVENTS, {
        method: "POST",
        body: params || {},
        fallback: {
          events: [
            {
              id: "1",
              name: "Summer Marketing Campaign",
              date: "2024-07-15",
              attendees: 250,
              revenue: 125000,
            },
          ],
        },
      }),
  }

  // Reports methods
  reports = {
    generate: (reportType: string, params?: any) =>
      this.request(API_ENDPOINTS.REPORTS.GENERATE, {
        method: "POST",
        body: { reportType, ...params },
        fallback: { reportId: Date.now().toString(), status: "generated" },
      }),

    download: (reportId: string) =>
      this.request(API_ENDPOINTS.REPORTS.DOWNLOAD, {
        method: "POST",
        body: { reportId },
        fallback: { downloadUrl: "#", filename: "report.pdf" },
      }),

    list: (params?: any) =>
      this.request(API_ENDPOINTS.REPORTS.LIST, {
        method: "POST",
        body: params || {},
        fallback: { reports: [] },
      }),
  }

  // Customer Journey methods
  customerJourney = {
    getAll: (params?: Record<string, string>) =>
      this.request(API_ENDPOINTS.CUSTOMER_JOURNEY.ALL, {
        method: "POST",
        body: params || {},
        fallback: {
          journeys: [
            {
              id: "1",
              customerName: "Tech Solutions Ltd",
              email: "<EMAIL>",
              phone: "+1234567890",
              stage: "lead",
              source: "Website",
              firstContactDate: new Date().toISOString(),
              notes: "Interested in digital marketing services",
              isActive: true,
            },
          ],
          pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
        },
      }),

    create: (data: any) =>
      this.request(API_ENDPOINTS.CUSTOMER_JOURNEY.CREATE, {
        method: "POST",
        body: data,
        fallback: { id: Date.now().toString(), ...data },
      }),

    update: (id: string, data: any) =>
      this.request(API_ENDPOINTS.CUSTOMER_JOURNEY.UPDATE, {
        method: "POST",
        body: { id, ...data },
        fallback: { id, ...data },
      }),

    delete: (id: string) =>
      this.request(API_ENDPOINTS.CUSTOMER_JOURNEY.DELETE, {
        method: "POST",
        body: { id },
        fallback: { success: true },
      }),

    bulkUpload: (data: any) =>
      this.request(API_ENDPOINTS.CUSTOMER_JOURNEY.BULK_UPLOAD, {
        method: "POST",
        body: data,
        fallback: { success: true, processed: 0 },
      }),
  }
}

export const apiService = new ApiService()
