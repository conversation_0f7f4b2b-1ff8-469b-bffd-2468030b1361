"use client";

import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Building2,
  MapPin,
  Tag,
  Newspaper,
  Radio,
  BarChart,
  Users,
  Layers,
  Contact,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";

export default function Masters() {
  const [searchQuery, setSearchQuery] = useState("");

  const masterCategories = [
    {
      title: "Business Masters",
      description: "Manage core business reference data",
      items: [
        {
          name: "Companies",
          icon: <Building2 className="h-5 w-5" />,
          href: "/companies",
        },
        {
          name: "Company Categories",
          icon: <Building2 className="h-5 w-5" />,
          href: "/masters/company-categories",
        },
        {
          name: "Cities",
          icon: <MapPin className="h-5 w-5" />,
          href: "/masters/cities",
        },
        {
          name: "Stages",
          icon: <Tag className="h-5 w-5" />,
          href: "/masters/stages",
        },
        {
          name: "Medium",
          icon: <Newspaper className="h-5 w-5" />,
          href: "/masters/medium",
        },
        {
          name: "Channels",
          icon: <Radio className="h-5 w-5" />,
          href: "/masters/channels",
        },
        {
          name: "Contacts",
          icon: <Users className="h-5 w-5" />,
          href: "/masters/customer-employees",
        },
      ],
    },
    {
      title: "Product Masters",
      description: "Manage product and service reference data",
      items: [
        {
          name: "Properties",
          icon: <BarChart className="h-5 w-5" />,
          href: "/masters/properties",
        },
        {
          name: "Variants",
          icon: <Layers className="h-5 w-5" />,
          href: "/masters/variants",
        },
      ],
    },
    {
      title: "User Masters",
      description: "Manage user reference data",
      items: [
        { name: "Users", icon: <Users className="h-5 w-5" />, href: "/users" },
      ],
    },
  ];

  // Filter masters based on search query
  const filteredCategories = searchQuery
    ? masterCategories
        .map((category) => ({
          ...category,
          items: category.items.filter((item) =>
            item.name.toLowerCase().includes(searchQuery.toLowerCase())
          ),
        }))
        .filter((category) => category.items.length > 0)
    : masterCategories;

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Masters</h1>
            <p className="text-muted-foreground">
              Manage all reference data for the Media4Growth BI system
            </p>
          </div>
          <div className="w-full sm:w-auto">
            <Input
              placeholder="Search masters..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:w-[250px]"
            />
          </div>
        </div>

        {filteredCategories.map((category, index) => (
          <div key={index} className="space-y-4">
            <h2 className="text-xl font-semibold">{category.title}</h2>
            <p className="text-muted-foreground">{category.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.items.map((item, itemIndex) => (
                <Link to={item.href} key={itemIndex}>
                  <Card className="hover:bg-accent/50 transition-colors cursor-pointer h-full">
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                      <CardTitle className="text-lg">{item.name}</CardTitle>
                      {item.icon}
                    </CardHeader>
                    <CardContent>
                      <CardDescription>
                        Manage {item.name.toLowerCase()} reference data
                      </CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
