// Mock data for development and testing

export const mockCustomers = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1234567890",
    company: "Acme Corp",
    status: "active",
    createdAt: "2024-01-15",
    totalSpent: 15000,
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1234567891",
    company: "Tech Solutions",
    status: "active",
    createdAt: "2024-01-20",
    totalSpent: 25000,
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1234567892",
    company: "Digital Agency",
    status: "inactive",
    createdAt: "2024-01-10",
    totalSpent: 8000,
  },
]

export const mockTransactions = [
  {
    id: "1",
    customerId: "1",
    amount: 5000,
    type: "sale",
    status: "completed",
    date: "2024-01-15",
    description: "Product purchase",
  },
  {
    id: "2",
    customerId: "2",
    amount: 12000,
    type: "sale",
    status: "completed",
    date: "2024-01-20",
    description: "Service contract",
  },
  {
    id: "3",
    customerId: "1",
    amount: 3000,
    type: "refund",
    status: "pending",
    date: "2024-01-25",
    description: "Product return",
  },
]

export const mockMasterData = {
  mediums: [
    { id: "1", name: "Online", description: "Digital channels", active: true },
    { id: "2", name: "Offline", description: "Physical channels", active: true },
    { id: "3", name: "Hybrid", description: "Mixed channels", active: true },
  ],
  channels: [
    { id: "1", name: "Website", mediumId: "1", active: true },
    { id: "2", name: "Mobile App", mediumId: "1", active: true },
    { id: "3", name: "Store", mediumId: "2", active: true },
    { id: "4", name: "Phone", mediumId: "2", active: true },
  ],
  properties: [
    { id: "1", name: "Location", type: "string", required: true },
    { id: "2", name: "Size", type: "number", required: false },
    { id: "3", name: "Category", type: "select", required: true },
  ],
  variants: [
    { id: "1", name: "Standard", description: "Standard variant", active: true },
    { id: "2", name: "Premium", description: "Premium variant", active: true },
    { id: "3", name: "Enterprise", description: "Enterprise variant", active: true },
  ],
  cities: [
    { id: "1", name: "New York", state: "NY", country: "USA", active: true },
    { id: "2", name: "Los Angeles", state: "CA", country: "USA", active: true },
    { id: "3", name: "Chicago", state: "IL", country: "USA", active: true },
  ],
  companies: [
    { id: "1", name: "Acme Corp", industry: "Technology", active: true },
    { id: "2", name: "Tech Solutions", industry: "Software", active: true },
    { id: "3", name: "Digital Agency", industry: "Marketing", active: true },
  ],
}

export const mockAnalytics = {
  dashboard: {
    totalCustomers: 1250,
    totalRevenue: 485000,
    totalTransactions: 3420,
    conversionRate: 12.5,
    revenueGrowth: 15.2,
    customerGrowth: 8.7,
  },
  revenue: {
    monthly: [
      { month: "Jan", revenue: 45000, target: 50000 },
      { month: "Feb", revenue: 52000, target: 55000 },
      { month: "Mar", revenue: 48000, target: 50000 },
      { month: "Apr", revenue: 61000, target: 60000 },
      { month: "May", revenue: 55000, target: 58000 },
      { month: "Jun", revenue: 67000, target: 65000 },
    ],
    byChannel: [
      { channel: "Website", revenue: 180000, percentage: 37 },
      { channel: "Mobile App", revenue: 145000, percentage: 30 },
      { channel: "Store", revenue: 98000, percentage: 20 },
      { channel: "Phone", revenue: 62000, percentage: 13 },
    ],
  },
  demographics: {
    ageGroups: [
      { group: "18-25", count: 320, percentage: 25.6 },
      { group: "26-35", count: 450, percentage: 36.0 },
      { group: "36-45", count: 280, percentage: 22.4 },
      { group: "46-55", count: 150, percentage: 12.0 },
      { group: "55+", count: 50, percentage: 4.0 },
    ],
    locations: [
      { city: "New York", customers: 420, percentage: 33.6 },
      { city: "Los Angeles", customers: 350, percentage: 28.0 },
      { city: "Chicago", customers: 280, percentage: 22.4 },
      { city: "Houston", customers: 200, percentage: 16.0 },
    ],
  },
}

// Mock API delay simulation
export const mockDelay = (ms = 500) => new Promise((resolve) => setTimeout(resolve, ms))

// Mock API response wrapper
export const mockApiResponse = (data, success = true, message = null) => ({
  success,
  data,
  message,
  timestamp: new Date().toISOString(),
})
