import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class UserService {
  async getAllUsers(
    params = {
      page: 1,
      limit: 100,
      search: "",
      role: "",
      department: "",
      status: "",
    }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.role) queryParams.append("role", params.role);
      if (params.department)
        queryParams.append("department", params.department);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/users?${queryString}`
        : `${API_URL}/users`;

      const response = await axios.get(url);

      // Ensure data is in expected format and transform status to isActive
      const usersData = Array.isArray(response.data) ? response.data : [];
      const transformedData = usersData.map((item) => ({
        ...item,
        isActive: item.status === "active", // Convert status enum to boolean for frontend
      }));

      return {
        users: transformedData,
      };
    } catch (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
  }

  async getUserById(id) {
    try {
      const response = await axios.get(`${API_URL}/users/${id}`);
      const user = response.data;

      // Transform status to isActive for frontend compatibility
      return {
        ...user,
        isActive: user.status === "active",
      };
    } catch (error) {
      console.error(`Error fetching user with ID ${id}:`, error);
      throw error;
    }
  }

  async createUser(userData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: userData.name,
        email: userData.email,
        role: userData.role,
        department: userData.department || "",
        phone: userData.phone || "",
        status: userData.isActive ? "active" : "inactive", // Convert boolean to enum
        // password will be generated by backend or sent separately
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/users`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  async updateUser(id, userData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: userData.name,
        email: userData.email,
        role: userData.role,
        department: userData.department || "",
        phone: userData.phone || "",
        status: userData.isActive ? "active" : "inactive", // Convert boolean to enum
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/users/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteUser(id) {
    try {
      const response = await axios.delete(`${API_URL}/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      throw error;
    }
  }

  async changeUserStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/users/${id}/status`, {
        status: status, // "active" or "inactive"
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing user status for ID ${id}:`, error);
      throw error;
    }
  }

  async resetUserPassword(id) {
    try {
      const response = await axios.post(
        `${API_URL}/users/${id}/reset-password`
      );
      return response.data;
    } catch (error) {
      console.error(`Error resetting password for user ID ${id}:`, error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const userService = new UserService();
export default userService;
