"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Input } from "../../components/ui/input.jsx"
import { Label } from "../../components/ui/label.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select.jsx"
import { Textarea } from "../../components/ui/textarea.jsx"
import { ArrowLeft, Save } from "lucide-react"
import { Link } from "react-router-dom"

export default function AddCustomerJourneyPage() {
  const [formData, setFormData] = useState({
    customer: "",
    stage: "",
    touchpoint: "",
    source: "",
    value: "",
    description: "",
    date: "",
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log("Creating customer journey:", formData)
    // In a real app, this would make an API call
    alert("Customer journey created successfully!")
  }

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Link to="/customer-journey">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Add Customer Journey</h1>
          <p className="text-muted-foreground">Create a new customer journey entry</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Journey Details</CardTitle>
          <CardDescription>Enter the customer journey information</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="customer">Customer</Label>
                <Input
                  id="customer"
                  placeholder="Enter customer name"
                  value={formData.customer}
                  onChange={(e) => handleInputChange("customer", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stage">Journey Stage</Label>
                <Select value={formData.stage} onValueChange={(value) => handleInputChange("stage", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select stage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="awareness">Awareness</SelectItem>
                    <SelectItem value="interest">Interest</SelectItem>
                    <SelectItem value="consideration">Consideration</SelectItem>
                    <SelectItem value="intent">Intent</SelectItem>
                    <SelectItem value="conversion">Conversion</SelectItem>
                    <SelectItem value="retention">Retention</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="touchpoint">Touchpoint</Label>
                <Select value={formData.touchpoint} onValueChange={(value) => handleInputChange("touchpoint", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select touchpoint" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="website">Website</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="social-media">Social Media</SelectItem>
                    <SelectItem value="sales-call">Sales Call</SelectItem>
                    <SelectItem value="webinar">Webinar</SelectItem>
                    <SelectItem value="event">Event</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="source">Source</Label>
                <Input
                  id="source"
                  placeholder="e.g., LinkedIn, Google, Direct"
                  value={formData.source}
                  onChange={(e) => handleInputChange("source", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="value">Potential Value ($)</Label>
                <Input
                  id="value"
                  type="number"
                  placeholder="Enter potential value"
                  value={formData.value}
                  onChange={(e) => handleInputChange("value", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter journey description or notes"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={4}
              />
            </div>

            <div className="flex space-x-4">
              <Button type="submit" className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Save Journey
              </Button>
              <Link to="/customer-journey" className="flex-1">
                <Button type="button" variant="outline" className="w-full">
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
