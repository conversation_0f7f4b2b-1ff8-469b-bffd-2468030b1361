"use client";

import { useState, useEffect } from "react";
import { Plus, MoreH<PERSON>zontal, Pencil, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { VariantForm } from "@/components/forms/variant-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import variantsService from "../../../services/variants.service";

export default function Variants() {
  const { toast } = useToast();
  const [variants, setVariants] = useState([]);
  const [properties, setProperties] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState(null);

  // Fetch variants from API using the service
  const fetchVariants = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await variantsService.getAllVariants({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const variantsArray = Array.isArray(result.variants)
        ? result.variants
        : [];
      setVariants(variantsArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch variants",
        variant: "destructive",
      });
      console.error("Error fetching variants:", error);
      setVariants([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch properties for dropdown
  const fetchProperties = async () => {
    try {
      const propertiesData = await variantsService.getProperties();
      setProperties(propertiesData);
    } catch (error) {
      console.error("Error fetching properties:", error);
      toast({
        title: "Warning",
        description: "Failed to load properties",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchVariants();
    fetchProperties();
  }, []);

  const handleAddVariant = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditVariant = (variant) => {
    setSelectedVariant(variant);
    setIsEditDialogOpen(true);
  };

  const handleDeleteVariant = (variant) => {
    setSelectedVariant(variant);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateVariant = async (data) => {
    try {
      await variantsService.createVariant(data);
      await fetchVariants();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Variant created successfully",
      });
    } catch (error) {
      console.error("Create variant error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create variant",
        variant: "destructive",
      });
    }
  };

  const handleUpdateVariant = async (data) => {
    try {
      await variantsService.updateVariant(selectedVariant.id, data);
      await fetchVariants();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Variant updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update variant",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteVariant = async () => {
    try {
      await variantsService.deleteVariant(selectedVariant.id);
      await fetchVariants();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Variant deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete variant",
        variant: "destructive",
      });
    }
  };

  const handleImportVariants = async (file) => {
    try {
      const result = await variantsService.bulkImportVariants(file);

      await fetchVariants(); // Refresh the variants list
      toast({
        title: "Success",
        description: "Variants imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import variants from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Variant Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "code",
      header: "Code",
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue("code")}</div>
      ),
    },
    {
      accessorKey: "propertyName",
      header: "Property",
      cell: ({ row }) => {
        const propertyName =
          row.original.propertyName ||
          properties.find((p) => p.id === row.original.propertyId)?.name ||
          "Unknown";
        return <div>{propertyName}</div>;
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.getValue("description");
        return description ? (
          <div className="max-w-[200px] truncate" title={description}>
            {description}
          </div>
        ) : (
          <div className="text-muted-foreground">-</div>
        );
      },
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("isActive");
        return (
          <Badge variant={isActive ? "success" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const variant = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleEditVariant(variant)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDeleteVariant(variant)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Variants Management</CardTitle>
              <CardDescription>
                Manage variants for different properties
              </CardDescription>
            </div>
            <Button onClick={handleAddVariant}>
              <Plus className="mr-2 h-4 w-4" />
              Add Variant
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={variants}
            searchKey="name"
            isLoading={isLoading}
            entityName="Variants"
            onImport={handleImportVariants}
            templatePath="/templates/variants_template.xlsx"
            acceptedFileTypes=".xlsx,.xls,.csv"
            fileDescription="File should contain columns: name, description, propertyId, status"
          />
        </CardContent>
      </Card>

      {/* Add Variant Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Variant</DialogTitle>
            <DialogDescription>
              Create a new variant for a property.
            </DialogDescription>
          </DialogHeader>
          <VariantForm
            onSubmit={handleCreateVariant}
            onCancel={() => setIsAddDialogOpen(false)}
            properties={properties}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Variant Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Variant</DialogTitle>
            <DialogDescription>
              Update the variant information.
            </DialogDescription>
          </DialogHeader>
          {selectedVariant && (
            <VariantForm
              defaultValues={selectedVariant}
              onSubmit={handleUpdateVariant}
              onCancel={() => setIsEditDialogOpen(false)}
              properties={properties}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Variant Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Variant</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this variant? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteVariant}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
