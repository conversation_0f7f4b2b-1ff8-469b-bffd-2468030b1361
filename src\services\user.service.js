import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class UserService {
  async getAllUsers(params = {}) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.role) queryParams.append("role", params.role);
      if (params.department)
        queryParams.append("department", params.department);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/users?${queryString}`
        : `${API_URL}/users`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
  }

  async getUserById(id) {
    try {
      const response = await axios.get(`${API_URL}/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user with ID ${id}:`, error);
      throw error;
    }
  }

  async createUser(userData) {
    try {
      const response = await axios.post(`${API_URL}/users`, userData);
      return response.data;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  async updateUser(id, userData) {
    try {
      const response = await axios.put(`${API_URL}/users/${id}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteUser(id) {
    try {
      const response = await axios.delete(`${API_URL}/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      throw error;
    }
  }

  async changeUserStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/users/${id}/status`, {
        status: status,
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing user status for ID ${id}:`, error);
      throw error;
    }
  }

  async getDepartments() {
    try {
      const response = await axios.get(`${API_URL}/departments`);
      return response.data;
    } catch (error) {
      console.error("Error fetching departments:", error);
      return [
        "Management",
        "Sales",
        "Marketing",
        "IT",
        "Finance",
        "HR",
        "Operations",
      ];
    }
  }
}

// Create and export a singleton instance
const userService = new UserService();
export default userService;
