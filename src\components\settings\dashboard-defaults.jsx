import { Button } from "../ui/button.jsx"
import { Label } from "../ui/label.jsx"
import { RadioGroup, RadioGroupItem } from "../ui/radio-group.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select.jsx"
import { Switch } from "../ui/switch.jsx"
import { Separator } from "../ui/separator.jsx"

export function DashboardDefaults() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Default View</h3>
        <p className="text-sm text-muted-foreground">
          Select which dashboard view appears by default when users log in.
        </p>
        <RadioGroup defaultValue="dashboard" className="space-y-2 mt-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="dashboard" id="dashboard" />
            <Label htmlFor="dashboard">Main Dashboard</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="analytics" id="analytics" />
            <Label htmlFor="analytics">Analytics</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="reports" id="reports" />
            <Label htmlFor="reports">Reports</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="customer-journey" id="customer-journey" />
            <Label htmlFor="customer-journey">Customer Journey</Label>
          </div>
        </RadioGroup>
      </div>

      <Separator />

      <div className="space-y-2">
        <h3 className="text-lg font-medium">Default Time Period</h3>
        <p className="text-sm text-muted-foreground">Set the default time period for dashboard metrics and charts.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div className="space-y-2">
            <Label htmlFor="time-period">Time Period</Label>
            <Select defaultValue="current-month">
              <SelectTrigger id="time-period">
                <SelectValue placeholder="Select time period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current-day">Current Day</SelectItem>
                <SelectItem value="current-week">Current Week</SelectItem>
                <SelectItem value="current-month">Current Month</SelectItem>
                <SelectItem value="current-quarter">Current Quarter</SelectItem>
                <SelectItem value="current-year">Current Year</SelectItem>
                <SelectItem value="last-30-days">Last 30 Days</SelectItem>
                <SelectItem value="last-90-days">Last 90 Days</SelectItem>
                <SelectItem value="last-12-months">Last 12 Months</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="comparison">Comparison</Label>
            <Select defaultValue="previous-period">
              <SelectTrigger id="comparison">
                <SelectValue placeholder="Select comparison" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="previous-period">Previous Period</SelectItem>
                <SelectItem value="previous-year">Previous Year</SelectItem>
                <SelectItem value="custom">Custom Comparison</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <h3 className="text-lg font-medium">Chart Preferences</h3>
        <p className="text-sm text-muted-foreground">Configure default chart types and appearance.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div className="space-y-2">
            <Label htmlFor="chart-theme">Color Theme</Label>
            <Select defaultValue="default">
              <SelectTrigger id="chart-theme">
                <SelectValue placeholder="Select color theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default</SelectItem>
                <SelectItem value="monochrome">Monochrome</SelectItem>
                <SelectItem value="pastel">Pastel</SelectItem>
                <SelectItem value="vibrant">Vibrant</SelectItem>
                <SelectItem value="corporate">Corporate</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="chart-animation">Animation Speed</Label>
            <Select defaultValue="normal">
              <SelectTrigger id="chart-animation">
                <SelectValue placeholder="Select animation speed" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="slow">Slow</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="fast">Fast</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex items-center space-x-2 mt-4">
          <Switch id="show-legends" defaultChecked />
          <Label htmlFor="show-legends">Show chart legends by default</Label>
        </div>
        <div className="flex items-center space-x-2 mt-2">
          <Switch id="show-tooltips" defaultChecked />
          <Label htmlFor="show-tooltips">Show tooltips on hover</Label>
        </div>
        <div className="flex items-center space-x-2 mt-2">
          <Switch id="enable-zoom" defaultChecked />
          <Label htmlFor="enable-zoom">Enable chart zooming</Label>
        </div>
      </div>

      <Separator />

      <div className="flex justify-end">
        <Button variant="outline" className="mr-2">
          Reset to Defaults
        </Button>
        <Button>Save Preferences</Button>
      </div>
    </div>
  )
}
