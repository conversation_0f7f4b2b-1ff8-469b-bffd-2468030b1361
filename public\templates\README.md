# Import Templates

This directory contains Excel template files for bulk data import functionality.

## Template Files Structure

### Cities Template (`cities_template.xlsx`)

**Required Columns:**

- `name` - City name (e.g., "Mumbai", "Delhi")
- `state` - State name (e.g., "Maharashtra", "Delhi")
- `country` - Country name (e.g., "India")
- `status` - Status value ("active" or "inactive")

**Sample Data:**

```
name        | state        | country | status
Mumbai      | Maharashtra  | India   | active
Delhi       | Delhi        | India   | active
Bangalore   | Karnataka    | India   | active
```

### Channels Template (`channels_template.xlsx`)

**Required Columns:**

- `name` - Channel name (e.g., "DDX Asia", "OAC Conference")
- `mediumId` - Medium ID (numeric, must exist in mediums table)
- `description` - Channel description (optional)
- `status` - Status value ("active" or "inactive")

**Sample Data:**

```
name         | mediumId | description                           | status
DDX Asia     | 1        | Digital marketing channel for Asia   | active
OAC Conference | 2      | Outdoor advertising conference       | active
Radio Campaign | 3      | Radio advertising campaign           | inactive
```

### Properties Template (`properties_template.xlsx`)

**Required Columns:**

- `name` - Property name (e.g., "Exhibitor", "Award Entry")
- `channelId` - Channel ID (numeric, must exist in channels table)
- `mediumId` - Medium ID (numeric, must exist in mediums table)
- `description` - Property description (optional)
- `price` - Property price (numeric, optional)
- `currency` - Currency code (e.g., "INR", "USD", optional, defaults to "INR")
- `status` - Status value ("active" or "inactive")

**Sample Data:**

```
name         | channelId | mediumId | description                    | price | currency | status
Exhibitor    | 1         | 1        | Exhibitor spaces at DDX Asia   | 50000 | INR      | active
Award Entry  | 2         | 1        | Entries for OAC Awards         | 15000 | INR      | active
Delegate     | 3         | 1        | Delegate passes for Talks OAH  | 25000 | INR      | inactive
```

### Transactions Template (`transactions_template.xlsx`)

**Required Columns:**

- `customerId` - Customer ID (numeric, must exist in customers table)
- `mediumId` - Medium ID (numeric, must exist in mediums table)
- `channelId` - Channel ID (numeric, must exist in channels table)
- `propertyId` - Property ID (numeric, must exist in properties table)
- `variantId` - Variant ID (numeric, optional, must exist in variants table if provided)
- `cityId` - City ID (numeric, optional, must exist in cities table if provided)
- `transactionDate` - Transaction date (YYYY-MM-DD format)
- `startDate` - Start date (YYYY-MM-DD format, optional)
- `endDate` - End date (YYYY-MM-DD format, optional)
- `amount` - Base amount (numeric, required)
- `taxAmount` - Tax amount (numeric, optional)
- `totalAmount` - Total amount (numeric, required)
- `paymentStatus` - Payment status ("pending", "paid", "partial", "cancelled")
- `paymentDate` - Payment date (YYYY-MM-DD format, optional)
- `paymentMethod` - Payment method (optional)
- `paymentReference` - Payment reference (optional)
- `invoiceNumber` - Invoice number (optional)
- `invoiceDate` - Invoice date (YYYY-MM-DD format, optional)
- `poNumber` - Purchase order number (optional)
- `description` - Transaction description (optional)
- `status` - Status value ("active" or "inactive")

**Sample Data:**

```
customerId | mediumId | channelId | propertyId | amount | totalAmount | paymentStatus | transactionDate | status
1          | 1        | 1         | 1          | 50000  | 59000       | paid          | 2024-01-15      | active
2          | 2        | 2         | 2          | 25000  | 29500       | pending       | 2024-01-20      | active
3          | 1        | 3         | 3          | 75000  | 88500       | partial       | 2024-01-25      | active
```

### Customers Template (`customers_template.xlsx`)

**Required Columns:**

- `name` - Customer name (required)
- `email` - Email address
- `phone` - Phone number
- `address` - Full address
- `cityId` - City ID (foreign key reference to cities table)
- `state` - State name
- `country` - Country name (default: "India")
- `pincode` - Postal code
- `website` - Website URL
- `industry` - Industry type
- `gstNumber` - GST number
- `panNumber` - PAN number
- `status` - Status ("active", "inactive", or "lead", default: "active")
- `notes` - Additional notes

**Sample Data:**

```
name              | email                    | phone          | address           | cityId | state       | country | pincode | website               | industry   | gstNumber       | panNumber  | status | notes
Tech Solutions Ltd| <EMAIL>| +91 9876543210 | 123 Business Park | 1      | Maharashtra | India   | 400001  | www.techsolutions.com | Technology | 27**********1Z5 | ********** | active | Leading tech company
Green Energy Corp | <EMAIL>     | +91 8765432109 | 456 Industrial Area| 2      | Karnataka   | India   | 560001  | www.greenenergy.com   | Energy     | 29**********2Y6 | ********** | active | Renewable energy solutions
Smart Retail Inc  | <EMAIL>    | +91 7654321098 | 789 Commercial St | 3      | Tamil Nadu  | India   | 600001  | www.smartretail.com   | Retail     | 33**********3X7 | ********** | lead   | Retail chain expansion
```

### Companies Template (`companies_template.xlsx`)

**Required Columns:**

- `name` - Company name (required)
- `email` - Email address (required)
- `phone` - Phone number
- `address` - Full address
- `city` - City name
- `state` - State name
- `country` - Country name (default: "India")
- `website` - Website URL
- `industry` - Industry type
- `type` - Company type ("client", "vendor", or "partner", default: "client")
- `status` - Status ("active", "inactive", or "pending", default: "active")
- `taxId` - Tax ID / GST number
- `registrationNumber` - Company registration number
- `notes` - Additional notes

**Sample Data:**

```
name                    | email                      | phone          | address                    | city      | state       | country | website                 | industry           | type   | status | taxId           | registrationNumber      | notes
R4G Media Solutions     | <EMAIL>          | +91 ********** | 123 Business Park Andheri  | Mumbai    | Maharashtra | India   | www.r4gmedia.com        | Media & Advertising| client | active | 27**********1Z5 | U74999MH2020PTC123456   | Leading media company
Tech Innovations Pvt   | <EMAIL>| +91 ********** | 456 Tech Hub Whitefield    | Bangalore | Karnataka   | India   | www.techinnovations.com | Technology         | vendor | active | 29**********2Y6 | U72200KA2019PTC987654   | Technology solutions provider
Green Energy Partners  | <EMAIL>      | +91 ********** | 789 Eco Complex CP         | Delhi     | Delhi       | India   | www.greenenergy.com     | Energy             | partner| active | 07**********3X7 | U40100DL2021PTC111222   | Renewable energy solutions
```

### Contacts Template (`contacts_template.xlsx`)

**Required Columns:**

- `name` - Contact name (required)
- `email` - Email address (required)
- `phone` - Phone number
- `company` - Company name
- `message` - Message or inquiry
- `status` - Status ("new", "in_progress", "resolved", or "closed", default: "new")
- `priority` - Priority ("low", "medium", "high", or "urgent", default: "medium")
- `assignedTo` - Assigned user ID (optional, numeric)
- `tags` - Tags separated by commas
- `notes` - Internal notes

**Sample Data:**

```
name        | email                  | phone          | company           | message                           | status      | priority | assignedTo | tags              | notes
John Doe    | <EMAIL>   | +91 9876543210 | Tech Solutions Ltd| Inquiry about digital marketing   | new         | high     |            | lead,marketing    | Initial contact from website
Jane Smith  | <EMAIL> | +91 8765432109 | Green Energy Corp | Request for proposal on campaign  | in_progress | medium   | 1          | proposal,advertising| Follow-up scheduled for next week
Mike Johnson| <EMAIL>       | +91 7654321098 | Startup Inc       | General inquiry about services    | new         | low      |            | inquiry,general   | Potential client for future projects
```

### Mediums Template (`mediums_template.xlsx`)

**Required Columns:**

- `name` - Medium name (required)
- `description` - Medium description
- `status` - Status ("active" or "inactive", default: "active")

**Sample Data:**

```
name        | description                                      | status
Print       | Traditional print media including newspapers     | active
Digital     | Online and digital advertising platforms        | active
Outdoor     | Billboards and outdoor advertising displays      | active
Radio       | Radio advertising and sponsorships              | active
Television  | TV commercials and program sponsorships         | active
Event       | Event marketing and sponsorships                | active
```

### Variants Template (`variants_template.xlsx`)

**Required Columns:**

- `name` - Variant name (required)
- `description` - Variant description
- `propertyId` - Property ID (foreign key reference to properties table)
- `status` - Status ("active" or "inactive", default: "active")

**Sample Data:**

```
name               | description                           | propertyId | status
Banner Ad          | Standard banner advertisement         | 1          | active
Full Page Ad       | Full page advertisement in publication| 1          | active
Half Page Ad       | Half page advertisement placement     | 1          | active
Video Ad           | Video advertisement placement         | 2          | active
Sponsored Content  | Sponsored article or content piece    | 1          | active
Email Newsletter   | Email newsletter advertisement        | 3          | active
```

## How to Create Templates

1. **Create Excel File**: Use Microsoft Excel or Google Sheets
2. **Add Headers**: First row should contain the column names exactly as specified
3. **Add Sample Data**: Include 2-3 sample rows to show the expected format
4. **Save as .xlsx**: Save the file in Excel format
5. **Place in Templates**: Put the file in `public/templates/` directory

## Template Naming Convention

- Use lowercase entity name + "\_template.xlsx"
- Examples:
  - `cities_template.xlsx`
  - `channels_template.xlsx`
  - `properties_template.xlsx`
  - `transactions_template.xlsx`
  - `customers_template.xlsx`
  - `products_template.xlsx`

## Adding New Templates

To add a new template for other entities:

1. Create the Excel file with proper structure
2. Save it in `public/templates/`
3. Update the component to use the template:

```javascript
<DataTable
  // ... other props
  templatePath="/templates/your_entity_template.xlsx"
  fileDescription="File should contain columns: col1, col2, col3"
/>
```

## File Access

Templates are served from the `public` directory and accessible via:

- URL: `http://localhost:3000/templates/filename.xlsx`
- Path: `/templates/filename.xlsx`

## Notes

- Templates should be small files (< 100KB)
- Include clear column headers
- Provide sample data for reference
- Use consistent naming conventions
- Test download functionality after adding new templates
