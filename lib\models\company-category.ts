export interface CompanyCategory {
  id: string
  name: string
  status: string
  createdAt: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  deletedBy?: number
  deletedAt?: string
}

export const sampleCompanyCategories: CompanyCategory[] = [
  {
    id: "CATEGORY-001",
    name: "Technology",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CATEGORY-002", 
    name: "Healthcare",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CATEGORY-003",
    name: "Finance",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CATEGORY-004",
    name: "Manufacturing",
    status: "active", 
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CATEGORY-005",
    name: "Retail",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "CATEGORY-006",
    name: "Education",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
];

export const createCompanyCategory = (data: Partial<CompanyCategory>): CompanyCategory => {
  return {
    id: data.id || "",
    name: data.name || "",
    status: data.status || "active",
    createdAt: data.createdAt || new Date().toISOString(),
    updatedAt: data.updatedAt,
    createdBy: data.createdBy,
    updatedBy: data.updatedBy,
    deletedBy: data.deletedBy,
    deletedAt: data.deletedAt,
  };
};

export const getCompanyCategoryDisplayName = (companyCategory: CompanyCategory): string => {
  return companyCategory.name || "Unknown Company Category";
};

export const isCompanyCategoryActive = (companyCategory: CompanyCategory): boolean => {
  return companyCategory.status === "active" && !companyCategory.deletedAt;
};
