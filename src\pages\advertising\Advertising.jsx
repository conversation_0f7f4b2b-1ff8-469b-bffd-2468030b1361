"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Input } from "../../components/ui/input.jsx"
import { Label } from "../../components/ui/label.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Progress } from "../../components/ui/progress.jsx"
import { TrendingUp, Eye, MousePointer, DollarSign, Target, Play, Pause, Plus } from "lucide-react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts"
import { PageHeader } from "../../components/page-header.jsx"
import { DataTable } from "../../components/data-table.jsx"

const campaignData = [
  {
    id: 1,
    name: "Summer Sale Campaign",
    platform: "Google Ads",
    budget: 5000,
    spent: 3250,
    impressions: 125000,
    clicks: 2450,
    conversions: 89,
    status: "Active",
    ctr: 1.96,
    cpc: 1.33,
    roas: 4.2,
  },
  {
    id: 2,
    name: "Brand Awareness Drive",
    platform: "Facebook",
    budget: 3000,
    spent: 2890,
    impressions: 89000,
    clicks: 1780,
    conversions: 45,
    status: "Active",
    ctr: 2.0,
    cpc: 1.62,
    roas: 3.1,
  },
  {
    id: 3,
    name: "Product Launch",
    platform: "Instagram",
    budget: 4500,
    spent: 4500,
    impressions: 156000,
    clicks: 3120,
    conversions: 156,
    status: "Completed",
    ctr: 2.0,
    cpc: 1.44,
    roas: 5.8,
  },
  {
    id: 4,
    name: "Retargeting Campaign",
    platform: "LinkedIn",
    budget: 2000,
    spent: 1250,
    impressions: 45000,
    clicks: 890,
    conversions: 34,
    status: "Paused",
    ctr: 1.98,
    cpc: 1.4,
    roas: 2.9,
  },
]

const performanceData = [
  { month: "Jan", impressions: 450000, clicks: 8900, conversions: 267 },
  { month: "Feb", impressions: 520000, clicks: 10400, conversions: 312 },
  { month: "Mar", impressions: 480000, clicks: 9600, conversions: 288 },
  { month: "Apr", impressions: 610000, clicks: 12200, conversions: 366 },
  { month: "May", impressions: 550000, clicks: 11000, conversions: 330 },
  { month: "Jun", impressions: 670000, clicks: 13400, conversions: 402 },
]

const platformDistribution = [
  { name: "Google Ads", value: 45, color: "#8884d8", spend: 12500 },
  { name: "Facebook", value: 30, color: "#82ca9d", spend: 8500 },
  { name: "Instagram", value: 15, color: "#ffc658", spend: 4200 },
  { name: "LinkedIn", value: 10, color: "#ff7300", spend: 2800 },
]

const columns = [
  {
    accessorKey: "name",
    header: "Campaign Name",
  },
  {
    accessorKey: "platform",
    header: "Platform",
    cell: ({ row }) => <Badge variant="outline">{row.getValue("platform")}</Badge>,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status")
      return (
        <Badge variant={status === "Active" ? "default" : status === "Completed" ? "secondary" : "destructive"}>
          {status}
        </Badge>
      )
    },
  },
  {
    accessorKey: "budget",
    header: "Budget",
    cell: ({ row }) => `$${row.getValue("budget").toLocaleString()}`,
  },
  {
    accessorKey: "spent",
    header: "Spent",
    cell: ({ row }) => `$${row.getValue("spent").toLocaleString()}`,
  },
  {
    accessorKey: "roas",
    header: "ROAS",
    cell: ({ row }) => `${row.getValue("roas")}x`,
  },
]

export default function AdvertisingPage() {
  const [selectedCampaign, setSelectedCampaign] = useState(null)
  const [newCampaignName, setNewCampaignName] = useState("")
  const [selectedPlatform, setSelectedPlatform] = useState("")

  const totalBudget = campaignData.reduce((sum, campaign) => sum + campaign.budget, 0)
  const totalSpent = campaignData.reduce((sum, campaign) => sum + campaign.spent, 0)
  const totalImpressions = campaignData.reduce((sum, campaign) => sum + campaign.impressions, 0)
  const totalClicks = campaignData.reduce((sum, campaign) => sum + campaign.clicks, 0)
  const totalConversions = campaignData.reduce((sum, campaign) => sum + campaign.conversions, 0)
  const avgCTR = ((totalClicks / totalImpressions) * 100).toFixed(2)
  const avgROAS = (campaignData.reduce((sum, campaign) => sum + campaign.roas, 0) / campaignData.length).toFixed(1)

  const handleCampaignAction = (campaignId, action) => {
    console.log(`${action} campaign ${campaignId}`)
    // In a real app, this would make an API call
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Advertising Management"
        description="Manage and optimize your advertising campaigns across platforms"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(totalImpressions / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12.5% from last month
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(totalClicks / 1000).toFixed(1)}K</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8.2% from last month
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average CTR</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgCTR}%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +0.3% from last month
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average ROAS</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgROAS}x</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +0.5x from last month
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="create">Create Campaign</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Campaigns</CardTitle>
              <CardDescription>Manage your advertising campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={campaignData} />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {campaignData.map((campaign) => (
              <Card key={campaign.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{campaign.name}</CardTitle>
                    <Badge
                      variant={
                        campaign.status === "Active"
                          ? "default"
                          : campaign.status === "Completed"
                            ? "secondary"
                            : "destructive"
                      }
                    >
                      {campaign.status}
                    </Badge>
                  </div>
                  <CardDescription>{campaign.platform}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Budget Usage</span>
                      <span>{((campaign.spent / campaign.budget) * 100).toFixed(0)}%</span>
                    </div>
                    <Progress value={(campaign.spent / campaign.budget) * 100} />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>${campaign.spent.toLocaleString()} spent</span>
                      <span>${campaign.budget.toLocaleString()} budget</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Impressions</div>
                      <div className="font-medium">{campaign.impressions.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Clicks</div>
                      <div className="font-medium">{campaign.clicks.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">CTR</div>
                      <div className="font-medium">{campaign.ctr}%</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">ROAS</div>
                      <div className="font-medium">{campaign.roas}x</div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    {campaign.status === "Active" ? (
                      <Button size="sm" variant="outline" onClick={() => handleCampaignAction(campaign.id, "pause")}>
                        <Pause className="h-4 w-4 mr-1" />
                        Pause
                      </Button>
                    ) : campaign.status === "Paused" ? (
                      <Button size="sm" onClick={() => handleCampaignAction(campaign.id, "resume")}>
                        <Play className="h-4 w-4 mr-1" />
                        Resume
                      </Button>
                    ) : null}
                    <Button size="sm" variant="outline">
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance</CardTitle>
                <CardDescription>Impressions, clicks, and conversions over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="impressions" stroke="#8884d8" name="Impressions" />
                    <Line type="monotone" dataKey="clicks" stroke="#82ca9d" name="Clicks" />
                    <Line type="monotone" dataKey="conversions" stroke="#ffc658" name="Conversions" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Trends</CardTitle>
                <CardDescription>Monthly conversion performance</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="conversions" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Platform Distribution</CardTitle>
              <CardDescription>Ad spend distribution across platforms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={platformDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {platformDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>

                <div className="space-y-4">
                  {platformDistribution.map((platform, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: platform.color }} />
                        <div>
                          <div className="font-medium">{platform.name}</div>
                          <div className="text-sm text-muted-foreground">${platform.spend.toLocaleString()} spent</div>
                        </div>
                      </div>
                      <Badge variant="outline">{platform.value}%</Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create New Campaign</CardTitle>
              <CardDescription>Set up a new advertising campaign</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="campaign-name">Campaign Name</Label>
                  <Input
                    id="campaign-name"
                    placeholder="Enter campaign name"
                    value={newCampaignName}
                    onChange={(e) => setNewCampaignName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="platform">Platform</Label>
                  <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select platform" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google Ads</SelectItem>
                      <SelectItem value="facebook">Facebook</SelectItem>
                      <SelectItem value="instagram">Instagram</SelectItem>
                      <SelectItem value="linkedin">LinkedIn</SelectItem>
                      <SelectItem value="twitter">Twitter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">Budget ($)</Label>
                  <Input id="budget" type="number" placeholder="Enter budget amount" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="objective">Campaign Objective</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select objective" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="awareness">Brand Awareness</SelectItem>
                      <SelectItem value="traffic">Website Traffic</SelectItem>
                      <SelectItem value="conversions">Conversions</SelectItem>
                      <SelectItem value="leads">Lead Generation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Create Campaign
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
