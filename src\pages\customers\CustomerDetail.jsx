"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom"
import {
  ArrowLeft,
  Building,
  Mail,
  Phone,
  MapPin,
  Tag,
  Calendar,
  Clock,
  MoreHorizontal,
  Plus,
  FileText,
  Edit,
  Globe,
} from "lucide-react"

import { <PERSON><PERSON> } from "../../components/ui/button.jsx"
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu.jsx"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "../../components/ui/dialog.jsx"
import { formatCurrency } from "../../lib/utils.js"
import { sampleCustomers } from "../../lib/models/customer.js"
import { sampleTransactions } from "../../lib/models/transaction.js"
import { CustomerForm } from "../../components/forms/customer-form.jsx"
import { TransactionForm } from "../../components/transactions/transaction-form.jsx"

function CustomerDetailPage() {
  const params = useParams()
  const customerId = params.id

  const [customer, setCustomer] = useState(null)
  const [customerTransactions, setCustomerTransactions] = useState([])
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAddTransactionDialogOpen, setIsAddTransactionDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // In a real app, this would be an API call
    const foundCustomer = sampleCustomers.find((c) => c.id === customerId)
    const filteredTransactions = sampleTransactions.filter((t) => t.customerId === customerId)

    setCustomer(foundCustomer || null)
    setCustomerTransactions(filteredTransactions)
    setIsLoading(false)
  }, [customerId])

  if (isLoading) {
    return <div className="flex-1 p-8">Loading...</div>
  }

  if (!customer) {
    return (
      <div className="flex-1 p-8">
        <h1 className="text-2xl font-bold mb-4">Customer not found</h1>
        <Button asChild>
          <Link to="/customers">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Customers
          </Link>
        </Button>
      </div>
    )
  }

  const handleEditCustomer = (data) => {
    console.log("Updating customer:", data)
    setIsEditDialogOpen(false)
  }

  const handleAddTransaction = (data) => {
    console.log("Adding transaction for customer:", customer.id, data)
    setIsAddTransactionDialogOpen(false)
  }

  const totalSpent = customerTransactions.reduce((sum, transaction) => sum + transaction.amount, 0)

  return (
    <div className="flex-1 space-y-6 p-6 md:p-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button asChild variant="outline" size="sm">
            <Link to="/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">{customer.name}</h1>
          <Badge variant={customer.status === "active" ? "success" : "secondary"}>{customer.status}</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setIsAddTransactionDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Transaction
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Customer
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <FileText className="mr-2 h-4 w-4" />
                Generate Report
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Mail className="mr-2 h-4 w-4" />
                Send Email
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Building className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Company</div>
                  <div>{customer.name}</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Email</div>
                  <div>{customer.email}</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Phone</div>
                  <div>{customer.phone}</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Address</div>
                  <div>{customer.address}</div>
                  <div>
                    {customer.city}, {customer.state}, {customer.country}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Type</div>
                  <div>{customer.type}</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Industry</div>
                  <div>{customer.industry}</div>
                </div>
              </div>
              {customer.website && (
                <div className="flex items-center gap-3">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Website</div>
                    <div>
                      <a href={customer.website} target="_blank" rel="noopener noreferrer" className="text-blue-600">
                        {customer.website}
                      </a>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Created At</div>
                  <div>{new Date(customer.createdAt).toLocaleDateString()}</div>
                </div>
              </div>
              {customer.updatedAt && (
                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Updated At</div>
                    <div>{new Date(customer.updatedAt).toLocaleDateString()}</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        <div className="md:col-span-2">
          <Tabs defaultValue="transactions">
            <TabsList>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
              <TabsTrigger value="overview">Overview</TabsTrigger>
            </TabsList>
            <TabsContent value="transactions">
              <div className="space-y-4">
                {customerTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-2">
                      <div className="text-sm font-medium">Amount</div>
                      <div>{formatCurrency(transaction.amount)}</div>
                    </div>
                    <div className="text-sm font-medium">{new Date(transaction.date).toLocaleDateString()}</div>
                  </div>
                ))}
              </div>
            </TabsContent>
            <TabsContent value="overview">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Total Spent</div>
                    <div>{formatCurrency(totalSpent)}</div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Customer</DialogTitle>
            <DialogDescription>Update customer information.</DialogDescription>
          </DialogHeader>
          <CustomerForm customer={customer} onSubmit={handleEditCustomer} />
        </DialogContent>
      </Dialog>

      <Dialog open={isAddTransactionDialogOpen} onOpenChange={setIsAddTransactionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Transaction</DialogTitle>
            <DialogDescription>Add a new transaction for this customer.</DialogDescription>
          </DialogHeader>
          <TransactionForm customerId={customer.id} onSubmit={handleAddTransaction} />
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default CustomerDetailPage
