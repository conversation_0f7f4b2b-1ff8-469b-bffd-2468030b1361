import { API_ENDPOINTS } from "../config/api";

// API service with correct URL configuration
const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:4000/api";

// Mock users for demo
const mockUsers = [
  {
    id: "1",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    department: "IT",
    isActive: true,
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "editor",
    department: "Management",
    isActive: true,
  },
];

// Simple auth without JWT
const authService = {
  login: async function (email, password) {
    return await this.request(API_ENDPOINTS.AUTH.LOGIN, {
      method: "POST",
      body: { email, password },
      fallback: {
        user: {
          id: "1",
          name: "Admin User",
          email: "<EMAIL>",
          role: "admin",
          department: "IT",
          isActive: true,
        },
        token: "mock-jwt-token",
      },
    });
  },

  logout: async () => {
    return { success: true };
  },

  me: async () => {
    const user = sessionStorage.getItem("user");
    if (user) {
      return {
        success: true,
        data: JSON.parse(user),
      };
    }
    return { success: false };
  },
};

// Mock data generators
const generateMockCustomers = (count = 50) => {
  const customers = [];
  const cities = [
    "Mumbai",
    "Delhi",
    "Bangalore",
    "Chennai",
    "Kolkata",
    "Hyderabad",
    "Pune",
    "Ahmedabad",
  ];
  const companies = [
    "TechCorp",
    "InnovateLtd",
    "GlobalSoft",
    "NextGen",
    "FutureTech",
    "SmartSol",
  ];
  const industries = [
    "Technology",
    "Healthcare",
    "Finance",
    "Education",
    "Retail",
    "Manufacturing",
  ];

  for (let i = 1; i <= count; i++) {
    customers.push({
      id: i.toString(),
      name: `${companies[Math.floor(Math.random() * companies.length)]} ${i}`,
      email: `customer${i}@example.com`,
      phone: `+91 ${Math.floor(Math.random() * **********) + **********}`,
      address: `${i} Business Street, Sector ${
        Math.floor(Math.random() * 50) + 1
      }`,
      city: cities[Math.floor(Math.random() * cities.length)],
      state: "Maharashtra",
      country: "India",
      type: Math.random() > 0.5 ? "Enterprise" : "SMB",
      industry: industries[Math.floor(Math.random() * industries.length)],
      website: `https://company${i}.com`,
      status: Math.random() > 0.2 ? "active" : "inactive",
      totalSpent: Math.floor(Math.random() * 500000) + 10000,
      lastActivity: new Date(
        Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
      ).toISOString(),
      createdAt: new Date(
        Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000
      ).toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }
  return customers;
};

// API service object
export const apiService = {
  auth: authService,

  customers: {
    getAll: async (params = {}) => {
      await new Promise((resolve) => setTimeout(resolve, 500));
      const customers = generateMockCustomers();

      // Apply filters if provided
      let filteredCustomers = customers;
      if (params.search) {
        filteredCustomers = customers.filter(
          (c) =>
            c.name.toLowerCase().includes(params.search.toLowerCase()) ||
            c.email.toLowerCase().includes(params.search.toLowerCase())
        );
      }
      if (params.status) {
        filteredCustomers = filteredCustomers.filter(
          (c) => c.status === params.status
        );
      }

      return {
        success: true,
        data: filteredCustomers,
        total: filteredCustomers.length,
      };
    },

    getById: async (id) => {
      await new Promise((resolve) => setTimeout(resolve, 300));
      const customers = generateMockCustomers();
      const customer = customers.find((c) => c.id === id);
      return customer
        ? { success: true, data: customer }
        : { success: false, error: "Customer not found" };
    },
  },

  analytics: {
    getDashboard: async () => {
      await new Promise((resolve) => setTimeout(resolve, 800));
      return {
        success: true,
        data: {
          totalRevenue: 12500000,
          totalCustomers: 2847,
          totalTransactions: 15632,
          conversionRate: 3.2,
          revenueGrowth: 12.5,
          customerGrowth: 8.3,
          transactionGrowth: 15.7,
          conversionGrowth: -2.1,
          monthlyRevenue: [
            { month: "Jan", revenue: 950000 },
            { month: "Feb", revenue: 1050000 },
            { month: "Mar", revenue: 1150000 },
            { month: "Apr", revenue: 1250000 },
            { month: "May", revenue: 1350000 },
            { month: "Jun", revenue: 1450000 },
            { month: "Jul", revenue: 1400000 },
            { month: "Aug", revenue: 1300000 },
            { month: "Sep", revenue: 1600000 },
            { month: "Oct", revenue: 1800000 },
            { month: "Nov", revenue: 2000000 },
            { month: "Dec", revenue: 1750000 },
          ],
          topChannels: [
            { name: "OA Mag", value: 35, revenue: 4375000 },
            { name: "New Media", value: 25, revenue: 3125000 },
            { name: "Events", value: 20, revenue: 2500000 },
            { name: "R4G", value: 15, revenue: 1875000 },
            { name: "M4G", value: 5, revenue: 625000 },
          ],
          topCities: [
            { city: "Mumbai", revenue: 3000000, customers: 450 },
            { city: "Delhi", revenue: 2500000, customers: 380 },
            { city: "Bangalore", revenue: 2200000, customers: 420 },
            { city: "Chennai", revenue: 1800000, customers: 290 },
            { city: "Pune", revenue: 1500000, customers: 250 },
          ],
        },
      };
    },
  },
};
