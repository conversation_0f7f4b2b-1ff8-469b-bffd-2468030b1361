"use client";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function ChannelForm({
  onSubmit,
  onCancel,
  defaultValues = {},
  mediums = [],
}) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      status: "active",
      ...defaultValues,
    },
  });

  const status = watch("status");
  const mediumId = watch("mediumId");

  const handleFormSubmit = (data) => {
    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Channel Name *</Label>
        <Input
          id="name"
          {...register("name", { required: "Channel name is required" })}
          placeholder="Enter channel name"
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="mediumId">Medium *</Label>
        <Select
          value={mediumId}
          onValueChange={(value) => setValue("mediumId", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select medium" />
          </SelectTrigger>
          <SelectContent>
            {mediums.map((medium) => (
              <SelectItem key={medium.id} value={medium.id.toString()}>
                {medium.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.mediumId && (
          <p className="text-sm text-red-500">{errors.mediumId.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          {...register("description")}
          placeholder="Enter channel description"
          rows={3}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="status"
          checked={status === "active"}
          onCheckedChange={(checked) =>
            setValue("status", checked ? "active" : "inactive")
          }
        />
        <Label htmlFor="status">Active</Label>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Save Channel</Button>
      </div>
    </form>
  );
}
