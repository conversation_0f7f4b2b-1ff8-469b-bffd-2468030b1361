"use client"

import { <PERSON>u } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useSidebar } from "@/components/ui/sidebar"

export function MobileHeader() {
  const { setOpenMobile } = useSidebar()
  return (
    <div className="flex md:hidden h-16 items-center border-b px-4">
      <Button variant="ghost" size="icon" onClick={() => setOpenMobile(true)} aria-label="Open menu">
        <Menu className="h-5 w-5" />
      </Button>
      <div className="ml-4 font-semibold">Media4Growth BI</div>
    </div>
  )
}
