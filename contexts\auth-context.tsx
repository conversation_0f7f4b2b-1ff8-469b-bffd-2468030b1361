"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState, type <PERSON>actNode } from "react"
import { useRout<PERSON> } from "next/navigation"
import { apiService } from "@/lib/services/api"

interface User {
  id: string
  name: string
  email: string
  role: string
  department?: string
  isActive: boolean
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const sessionvalues = sessionStorage.getItem('user');
      setIsLoading(false)
    //   const response = await apiService.auth.me()

    //   if (response.success && response.data) {
        setUser(JSON.parse(sessionvalues));
    //   } else {
    //     setUser(null)
    //   }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const response = await apiService.auth.login(email, password);
      console.log("res11111212",response?.status);
      if (response?.status) {
        sessionStorage.setItem('user',JSON.stringify(response?.result?.user));
        sessionStorage.setItem('token',JSON.stringify(response?.result?.token));
        console.log("res",response.data);
        setUser(response?.result?.user)
        return { success: true }
      }

      return {
        success: false,
        message: response.message || response.error || "Invalid email or password",
      }
    } catch (error: any) {
      console.error("Login error:", error)
      return {
        success: false,
        message: error.message || "An error occurred during login",
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
        sessionStorage.clear();
    //   await apiService.auth.logout()
      setUser(null)
      router.push("/login")
    } catch (error) {
      console.error("Logout error:", error)
      // Even if logout API fails, clear local state
      setUser(null)
      router.push("/login")
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    try {
      const response = await apiService.auth.me()

      if (response.success && response.data) {
        setUser(response.data)
      }
    } catch (error) {
      console.error("Failed to refresh user:", error)
    }
  }

  return <AuthContext.Provider value={{ user, isLoading, login, logout, refreshUser }}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
