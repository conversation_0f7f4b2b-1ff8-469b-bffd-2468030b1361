import * as z from "zod";

// Customer validation schema
export const customerSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z
    .string()
    .min(10, { message: "Phone number must be at least 10 digits" }),
  address: z
    .string()
    .min(5, { message: "Address must be at least 5 characters" }),
  city: z.string().min(1, { message: "City is required" }),
  status: z.string().min(1, { message: "Status is required" }),
  type: z.string().min(1, { message: "Type is required" }),
});

// City validation schema
export const citySchema = z.object({
  name: z
    .string()
    .min(2, { message: "City name must be at least 2 characters" }),
  state: z.string().min(2, { message: "State must be at least 2 characters" }),
  country: z
    .string()
    .min(2, { message: "Country must be at least 2 characters" }),
  isActive: z.string().default("yes"),
});

// Stage validation schema
export const stageSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Stage name must be at least 2 characters" }),
  slug: z.string().min(2, { message: "Slug must be at least 2 characters" }),
  status: z.enum(["active", "inactive"]).default("active"),
});

// Company Category validation schema
export const companyCategorySchema = z.object({
  name: z
    .string()
    .min(2, { message: "Company category name must be at least 2 characters" }),
  status: z.enum(["active", "inactive"]).default("active"),
});

// Medium validation schema
export const mediumSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Medium name must be at least 2 characters" }),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// Channel validation schema
export const channelSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Channel name must be at least 2 characters" }),
  mediumId: z.string().min(1, { message: "Medium is required" }),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// Property validation schema
export const propertySchema = z.object({
  name: z
    .string()
    .min(2, { message: "Property name must be at least 2 characters" }),
  channelId: z.string().min(1, { message: "Channel is required" }),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// Variant validation schema
export const variantSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Variant name must be at least 2 characters" }),
  propertyId: z.string().min(1, { message: "Property is required" }),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// Import validation schema
export const importSchema = z.object({
  file: z.instanceof(File, { message: "File is required" }),
  type: z.string().min(1, { message: "Import type is required" }),
});
