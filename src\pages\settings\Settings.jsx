import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"

export default function SettingsPage() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Application Settings</CardTitle>
          <CardDescription>Manage your application preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="text-lg text-muted-foreground">Settings Panel</div>
            <p className="text-sm text-muted-foreground mt-2">Settings configuration will be available here</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
