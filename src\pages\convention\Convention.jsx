"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { <PERSON><PERSON> } from "../../components/ui/button.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Calendar, MapPin, Users, Star, Download } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts"
import { PageHeader } from "../../components/page-header.jsx"
import { DataTable } from "../../components/data-table.jsx"

const conventionData = [
  {
    id: 1,
    name: "Tech Innovation Summit 2024",
    date: "2024-03-15",
    location: "San Francisco, CA",
    attendees: 1250,
    rating: 4.8,
    status: "Completed",
    revenue: 185000,
  },
  {
    id: 2,
    name: "Digital Marketing Expo",
    date: "2024-04-22",
    location: "New York, NY",
    attendees: 890,
    rating: 4.6,
    status: "Completed",
    revenue: 125000,
  },
  {
    id: 3,
    name: "Future of AI Conference",
    date: "2024-05-10",
    location: "Austin, TX",
    attendees: 1450,
    rating: 4.9,
    status: "Upcoming",
    revenue: 220000,
  },
  {
    id: 4,
    name: "Startup Showcase",
    date: "2024-06-05",
    location: "Seattle, WA",
    attendees: 650,
    rating: 4.5,
    status: "Upcoming",
    revenue: 95000,
  },
]

const attendanceData = [
  { day: "Day 1", morning: 1200, afternoon: 1150, evening: 980 },
  { day: "Day 2", morning: 1180, afternoon: 1220, evening: 1050 },
  { day: "Day 3", morning: 1100, afternoon: 1080, evening: 920 },
]

const revenueData = [
  { month: "Jan", revenue: 145000, target: 150000 },
  { month: "Feb", revenue: 165000, target: 160000 },
  { month: "Mar", revenue: 185000, target: 170000 },
  { month: "Apr", revenue: 125000, target: 140000 },
  { month: "May", revenue: 220000, target: 200000 },
  { month: "Jun", revenue: 95000, target: 180000 },
]

const columns = [
  {
    accessorKey: "name",
    header: "Convention Name",
  },
  {
    accessorKey: "date",
    header: "Date",
  },
  {
    accessorKey: "location",
    header: "Location",
    cell: ({ row }) => (
      <div className="flex items-center">
        <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
        {row.getValue("location")}
      </div>
    ),
  },
  {
    accessorKey: "attendees",
    header: "Attendees",
    cell: ({ row }) => <Badge variant="secondary">{row.getValue("attendees")} people</Badge>,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <Badge variant={row.getValue("status") === "Completed" ? "default" : "outline"}>{row.getValue("status")}</Badge>
    ),
  },
  {
    accessorKey: "rating",
    header: "Rating",
    cell: ({ row }) => (
      <div className="flex items-center">
        <Star className="h-4 w-4 mr-1 text-yellow-500 fill-current" />
        {row.getValue("rating")}
      </div>
    ),
  },
]

export default function ConventionPage() {
  const [selectedConvention, setSelectedConvention] = useState(null)
  const [loading, setLoading] = useState(false)

  const totalAttendees = conventionData.reduce((sum, conv) => sum + conv.attendees, 0)
  const totalRevenue = conventionData.reduce((sum, conv) => sum + conv.revenue, 0)
  const avgRating = conventionData.reduce((sum, conv) => sum + conv.rating, 0) / conventionData.length

  const handleExportReport = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      alert("Convention report exported successfully!")
    }, 2000)
  }

  return (
    <div className="space-y-6">
      <PageHeader title="Convention Management" description="Manage and analyze convention events and performance" />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conventions</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{conventionData.length}</div>
            <p className="text-xs text-muted-foreground">2 upcoming events</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Attendees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAttendees.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across all conventions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgRating.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">Out of 5.0 stars</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(totalRevenue / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">From all conventions</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button onClick={handleExportReport} disabled={loading}>
          <Download className="h-4 w-4 mr-2" />
          {loading ? "Exporting..." : "Export Report"}
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="conventions">Conventions</TabsTrigger>
          <TabsTrigger value="attendance">Attendance</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Convention Performance</CardTitle>
                <CardDescription>Revenue performance vs targets</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="revenue" fill="#8884d8" name="Actual Revenue" />
                    <Bar dataKey="target" fill="#82ca9d" name="Target" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upcoming Conventions</CardTitle>
                <CardDescription>Next scheduled events</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {conventionData
                  .filter((conv) => conv.status === "Upcoming")
                  .map((convention) => (
                    <div key={convention.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium">{convention.name}</div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {convention.date}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {convention.location}
                        </div>
                      </div>
                      <Badge variant="outline">{convention.attendees} attendees</Badge>
                    </div>
                  ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="conventions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Conventions</CardTitle>
              <CardDescription>Complete list of convention events</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={conventionData} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attendance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Attendance Patterns</CardTitle>
              <CardDescription>Attendance breakdown by time of day</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={attendanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="morning" fill="#8884d8" name="Morning" />
                  <Bar dataKey="afternoon" fill="#82ca9d" name="Afternoon" />
                  <Bar dataKey="evening" fill="#ffc658" name="Evening" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Trends</CardTitle>
              <CardDescription>Monthly revenue from conventions</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} name="Revenue" />
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Target"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
