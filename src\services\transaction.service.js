import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class TransactionService {
  async getAllTransactions(
    params = { page: 1, limit: 100, search: "", status: "", paymentStatus: "" }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);
      if (params.paymentStatus)
        queryParams.append("paymentStatus", params.paymentStatus);
      if (params.customerId)
        queryParams.append("customerId", params.customerId);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/transactions?${queryString}`
        : `${API_URL}/transactions`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const transactionsData = Array.isArray(response.data)
        ? response.data
        : [];

      return {
        transactions: transactionsData,
      };
    } catch (error) {
      console.error("Error fetching transactions:", error);
      throw error;
    }
  }

  async getTransactionById(id) {
    try {
      const response = await axios.get(`${API_URL}/transactions/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching transaction with ID ${id}:`, error);
      throw error;
    }
  }

  async createTransaction(transactionData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        customerId: transactionData.customerId,
        mediumId: transactionData.mediumId,
        channelId: transactionData.channelId,
        propertyId: transactionData.propertyId,
        variantId: transactionData.variantId || null,
        cityId: transactionData.cityId || null,
        stage: transactionData.stage || null,
        quantity: transactionData.quantity
          ? parseInt(transactionData.quantity)
          : null,
        month: transactionData.month || null,
        year: transactionData.year ? parseInt(transactionData.year) : null,
        revenue: transactionData.revenue
          ? parseFloat(transactionData.revenue)
          : null,
        notes: transactionData.notes || null,
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/transactions`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating transaction:", error);
      throw error;
    }
  }

  async updateTransaction(id, transactionData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        customerId: transactionData.customerId,
        mediumId: transactionData.mediumId,
        channelId: transactionData.channelId,
        propertyId: transactionData.propertyId,
        variantId: transactionData.variantId || null,
        cityId: transactionData.cityId || null,
        stage: transactionData.stage || null,
        quantity: transactionData.quantity
          ? parseInt(transactionData.quantity)
          : null,
        month: transactionData.month || null,
        year: transactionData.year ? parseInt(transactionData.year) : null,
        revenue: transactionData.revenue
          ? parseFloat(transactionData.revenue)
          : null,
        notes: transactionData.notes || null,
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(
        `${API_URL}/transactions/${id}`,
        apiData
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating transaction with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteTransaction(id) {
    try {
      const response = await axios.delete(`${API_URL}/transactions/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting transaction with ID ${id}:`, error);
      throw error;
    }
  }

  async changeTransactionStatus(id, status) {
    try {
      const response = await axios.patch(
        `${API_URL}/transactions/${id}/status`,
        {
          status: status,
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error changing transaction status for ID ${id}:`, error);
      throw error;
    }
  }

  async changePaymentStatus(id, paymentStatus) {
    try {
      const response = await axios.patch(
        `${API_URL}/transactions/${id}/payment-status`,
        {
          paymentStatus: paymentStatus,
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error changing payment status for ID ${id}:`, error);
      throw error;
    }
  }

  // Helper method to get customers for dropdown
  async getCustomers() {
    try {
      const response = await axios.get(`${API_URL}/customers`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching customers:", error);
      return [];
    }
  }

  // Helper method to get mediums for dropdown
  async getMediums() {
    try {
      const response = await axios.get(`${API_URL}/mediums`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching mediums:", error);
      return [];
    }
  }

  // Helper method to get channels for dropdown
  async getChannels() {
    try {
      const response = await axios.get(`${API_URL}/channels`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching channels:", error);
      return [];
    }
  }

  // Helper method to get properties for dropdown
  async getProperties() {
    try {
      const response = await axios.get(`${API_URL}/properties`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching properties:", error);
      return [];
    }
  }

  // Helper method to get variants for dropdown
  async getVariants() {
    try {
      const response = await axios.get(`${API_URL}/variants`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching variants:", error);
      return [];
    }
  }

  // Helper method to get cities for dropdown
  async getCities() {
    try {
      const response = await axios.get(`${API_URL}/cities`);
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching cities:", error);
      return [];
    }
  }

  // Helper method to get transactions by customer
  async getTransactionsByCustomer(customerId) {
    try {
      const response = await axios.get(
        `${API_URL}/transactions/by-customer/${customerId}`
      );
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error(
        `Error fetching transactions for customer ${customerId}:`,
        error
      );
      return [];
    }
  }

  async bulkImportTransactions(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/transactions/insert-transactions`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing transactions from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const transactionService = new TransactionService();
export default transactionService;
