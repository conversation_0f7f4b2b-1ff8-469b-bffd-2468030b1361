import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class ChannelService {
  async getAllChannels(
    params = { page: 1, limit: 100, search: "", mediumId: "", status: "" }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.mediumId) queryParams.append("mediumId", params.mediumId);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/channels?${queryString}`
        : `${API_URL}/channels`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const channelsData = Array.isArray(response.data) ? response.data : [];

      return {
        channels: channelsData,
      };
    } catch (error) {
      console.error("Error fetching channels:", error);
      throw error;
    }
  }

  async getChannelById(id) {
    try {
      const response = await axios.get(`${API_URL}/channels/${id}`);
      const channel = response.data;
      return channel;
    } catch (error) {
      console.error(`Error fetching channel with ID ${id}:`, error);
      throw error;
    }
  }

  async createChannel(channelData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: channelData.name,
        mediumId: parseInt(channelData.mediumId), // Ensure it's an integer
        description: channelData.description || "",
        status: channelData.status || "active",
        // slug and code will be handled by backend
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/channels`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating channel:", error);
      throw error;
    }
  }

  async updateChannel(id, channelData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: channelData.name,
        mediumId: parseInt(channelData.mediumId), // Ensure it's an integer
        description: channelData.description || "",
        status: channelData.status || "active",
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/channels/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating channel with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteChannel(id) {
    try {
      const response = await axios.delete(`${API_URL}/channels/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting channel with ID ${id}:`, error);
      throw error;
    }
  }

  async changeChannelStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/channels/${id}/status`, {
        status: status, // "active" or "inactive"
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing channel status for ID ${id}:`, error);
      throw error;
    }
  }

  async getChannelsByMedium(mediumId) {
    try {
      const response = await axios.get(
        `${API_URL}/channels/by-medium/${mediumId}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching channels by medium ${mediumId}:`, error);
      throw error;
    }
  }

  async searchChannels(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/channels/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(`Error searching channels with term ${searchTerm}:`, error);
      throw error;
    }
  }

  async getChannelStats() {
    try {
      const response = await axios.get(`${API_URL}/channels/stats`);
      return response.data;
    } catch (error) {
      console.error("Error fetching channel statistics:", error);
      throw error;
    }
  }

  async bulkUpdateChannels(channelIds, updateData) {
    try {
      const response = await axios.patch(`${API_URL}/channels/bulk-update`, {
        channelIds,
        updateData,
      });
      return response.data;
    } catch (error) {
      console.error("Error bulk updating channels:", error);
      throw error;
    }
  }

  async duplicateChannel(id) {
    try {
      const response = await axios.post(`${API_URL}/channels/${id}/duplicate`);
      return response.data;
    } catch (error) {
      console.error(`Error duplicating channel with ID ${id}:`, error);
      throw error;
    }
  }

  async bulkImportChannels(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/channels/insert-channels`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing channels from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const channelService = new ChannelService();
export default channelService;
