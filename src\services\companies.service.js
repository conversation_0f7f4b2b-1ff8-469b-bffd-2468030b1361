import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class CompanyService {
  async getAllCompanies(
    params = {
      page: 1,
      limit: 100,
      search: "",
      type: "",
      industry: "",
      status: "",
    }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.type) queryParams.append("type", params.type);
      if (params.industry) queryParams.append("industry", params.industry);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/companies?${queryString}`
        : `${API_URL}/companies`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const companiesData = Array.isArray(response.data) ? response.data : [];

      return {
        companies: companiesData,
      };
    } catch (error) {
      console.error("Error fetching companies:", error);
      throw error;
    }
  }

  async getCompanyById(id) {
    try {
      const response = await axios.get(`${API_URL}/companies/${id}`);
      const company = response.data;
      return company;
    } catch (error) {
      console.error(`Error fetching company with ID ${id}:`, error);
      throw error;
    }
  }

  async createCompany(companyData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: companyData.name,
        email: companyData.email,
        phone: companyData.phone || "",
        address: companyData.address || "",
        city: companyData.city || "",
        state: companyData.state || "",
        country: companyData.country || "India",
        website: companyData.website || "",
        industry: companyData.industry || "",
        type: companyData.type || "client", // client, vendor, partner
        status: companyData.status || "active", // active, inactive, pending
        taxId: companyData.taxId || "",
        registrationNumber: companyData.registrationNumber || "",
        notes: companyData.notes || "",
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/companies`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating company:", error);
      throw error;
    }
  }

  async updateCompany(id, companyData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: companyData.name,
        email: companyData.email,
        phone: companyData.phone || "",
        address: companyData.address || "",
        city: companyData.city || "",
        state: companyData.state || "",
        country: companyData.country || "India",
        website: companyData.website || "",
        industry: companyData.industry || "",
        type: companyData.type || "client",
        status: companyData.status || "active",
        taxId: companyData.taxId || "",
        registrationNumber: companyData.registrationNumber || "",
        notes: companyData.notes || "",
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/companies/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating company with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteCompany(id) {
    try {
      const response = await axios.delete(`${API_URL}/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting company with ID ${id}:`, error);
      throw error;
    }
  }

  async changeCompanyStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/companies/${id}/status`, {
        status: status, // "active", "inactive", or "pending"
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing company status for ID ${id}:`, error);
      throw error;
    }
  }

  async getCompaniesByType(type) {
    try {
      const response = await axios.get(`${API_URL}/companies/by-type/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching companies by type ${type}:`, error);
      throw error;
    }
  }

  async getCompaniesByIndustry(industry) {
    try {
      const response = await axios.get(
        `${API_URL}/companies/by-industry/${industry}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching companies by industry ${industry}:`, error);
      throw error;
    }
  }

  async searchCompanies(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/companies/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(
        `Error searching companies with term ${searchTerm}:`,
        error
      );
      throw error;
    }
  }

  async bulkImportCompanies(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/companies/insert-companies`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing companies from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const companyService = new CompanyService();
export default companyService;
