import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";
const API_DOWNLOAD_URL = "http://localhost:4000";

class ReportsService {
  /**
   * Get filter options for reports
   * @returns {Promise<Object>} Filter options from API
   */
  async getFilterOptions() {
    try {
      const response = await axios.get(
        `${API_URL}/transactions/dashboard/filter-options`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching filter options:", error);
      // Return fallback static options
      return {
        years: ["2023", "2024", "2025"],
        months: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ],
        mediums: ["Print", "Digital", "Outdoor", "Event"],
        channels: ["OA Mag", "New Media", "Events", "R4G", "M4G"],
        properties: [
          "Website",
          "Magazine",
          "Newsletter",
          "Convention",
          "Awards",
        ],
        cities: [
          "Mumbai",
          "Bengaluru",
          "Delhi",
          "Pune",
          "Chennai",
          "Hyderabad",
        ],
        customers: [
          "Xireme Media",
          "Singpost",
          "SabRentKaro",
          "JBM Group",
          "Sakal",
        ],
        stages: [
          "Inquiry",
          "Attendee",
          "Proposal",
          "Negotiation",
          "Confirmed",
          "Completed",
          "Cancelled",
        ],
      };
    }
  }

  /**
   * Generate transaction report
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Object>} Report generation response with downloadUrl, totalRecords, totalRevenue
   */
  async generateTransactionReport(filters = {}) {
    try {
      const response = await axios.post(
        `${API_URL}/transactions/reports/transactions`,
        filters
      );
      return response.data;
    } catch (error) {
      console.error("Error generating transaction report:", error);
      throw error;
    }
  }

  /**
   * Generate stage customer report
   * @param {Object} filters - Filter parameters with stages
   * @returns {Promise<Object>} Report generation response with downloadUrl, totalRecords, stages
   */
  async generateStageCustomerReport(filters = {}) {
    try {
      const response = await axios.post(
        `${API_URL}/transactions/reports/stage-customers`,
        filters
      );
      return response.data;
    } catch (error) {
      console.error("Error generating stage customer report:", error);
      throw error;
    }
  }

  /**
   * Download report file
   * @param {string} downloadUrl - The download URL from the report generation response
   * @returns {Promise<void>}
   */
  async downloadReport(downloadUrl) {
    try {
      // Create a temporary link element and trigger download
      const link = document.createElement("a");
      link.href = `${API_DOWNLOAD_URL}${downloadUrl}`;
      link.download = downloadUrl.split("/").pop(); // Extract filename from URL
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading report:", error);
      throw error;
    }
  }

  /**
   * Build stage customer report request body
   * @param {Array} selectedStages - Selected stages array
   * @returns {Object} Formatted request body for stage customer report
   */
  buildStageCustomerRequestBody(selectedStages = []) {
    // If no stages selected, return empty object (all stages)
    if (selectedStages.length === 0) {
      return {};
    }

    // If single stage selected, use "stage" property
    if (selectedStages.length === 1) {
      return {
        stage: selectedStages[0],
      };
    }

    // If multiple stages selected, use "stages" property
    return {
      stages: selectedStages,
    };
  }

  /**
   * Build filter request body for API call
   * @param {Object} filters - Filter parameters
   * @returns {Object} Formatted request body
   */
  buildFilterRequestBody(filters) {
    const {
      yearFilters = [],
      monthFilters = [],
      mediumFilters = [],
      channelFilters = [],
      propertyFilters = [],
      cityFilters = [],
      customerFilters = [],
      stageFilters = [],
      startMonth,
      startYear,
      endMonth,
      endYear,
      isCustomDateRange = false,
    } = filters;

    // If custom date range is selected, use startMonth/startYear and endMonth/endYear
    if (isCustomDateRange && startMonth && startYear && endMonth && endYear) {
      return {
        startMonth,
        startYear,
        endMonth,
        endYear,
        mediums: mediumFilters,
        channels: channelFilters,
        properties: propertyFilters,
        cities: cityFilters,
        customers: customerFilters,
        stages: stageFilters,
      };
    }

    // Otherwise use regular filters
    return {
      years: yearFilters,
      months: monthFilters,
      mediums: mediumFilters,
      channels: channelFilters,
      properties: propertyFilters,
      cities: cityFilters,
      customers: customerFilters,
      stages: stageFilters,
    };
  }
}

// Create and export service instance
const reportsService = new ReportsService();
export default reportsService;
