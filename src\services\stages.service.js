import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class StageService {
  async getAllStages(
    params = {
      page: 1,
      limit: 100,
      search: "",
      status: "",
    }
  ) {
    try {
      const response = await axios.get(`${API_URL}/stages`, {
        params: {
          page: params.page || 1,
          limit: params.limit || 100,
          search: params.search || "",
          status: params.status || "",
        },
      });

      // Handle different response formats
      if (response.data && response.data.data) {
        // If response has nested data structure
        return {
          stages: response.data.data.stages || response.data.data,
          total: response.data.data.total || response.data.total,
          page: response.data.data.page || response.data.page,
          limit: response.data.data.limit || response.data.limit,
        };
      } else if (response.data && Array.isArray(response.data.stages)) {
        // If response has stages array directly
        return response.data;
      } else if (response.data && Array.isArray(response.data)) {
        // If response is directly an array
        return {
          stages: response.data,
          total: response.data.length,
          page: 1,
          limit: response.data.length,
        };
      } else {
        // Fallback
        return {
          stages: [],
          total: 0,
          page: 1,
          limit: 100,
        };
      }
    } catch (error) {
      console.error("Error fetching stages:", error);
      throw error;
    }
  }

  async createStage(stageData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: stageData.name,
        status: stageData.status || "active",
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/stages`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating stage:", error);
      throw error;
    }
  }

  async updateStage(id, stageData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: stageData.name,
        status: stageData.status || "active",
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/stages/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating stage with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteStage(id) {
    try {
      const response = await axios.delete(`${API_URL}/stages/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting stage with ID ${id}:`, error);
      throw error;
    }
  }

  async getStageById(id) {
    try {
      const response = await axios.get(`${API_URL}/stages/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching stage with ID ${id}:`, error);
      throw error;
    }
  }

  async importStages(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(`${API_URL}/stages/import`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error importing stages:", error);
      throw error;
    }
  }

  async exportStages(format = "xlsx") {
    try {
      const response = await axios.get(`${API_URL}/stages/export`, {
        params: { format },
        responseType: "blob",
      });
      return response.data;
    } catch (error) {
      console.error(`Error exporting stages in ${format} format:`, error);
      throw error;
    }
  }

  async searchStages(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/stages/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(`Error searching stages with term ${searchTerm}:`, error);
      throw error;
    }
  }


  // Utility method to validate stage data
  validateStageData(stageData) {
    const errors = {};

    if (!stageData.name || stageData.name.trim().length === 0) {
      errors.name = "Stage name is required";
    }

    if (stageData.name && stageData.name.trim().length < 2) {
      errors.name = "Stage name must be at least 2 characters";
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }
}

// Export a singleton instance
const stageService = new StageService();
export default stageService;
