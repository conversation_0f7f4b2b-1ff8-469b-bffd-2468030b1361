"use client";

import { useState, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Button } from "../ui/button.jsx";
import { Input } from "../ui/input.jsx";
import { Label } from "../ui/label.jsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select.jsx";
import { Textarea } from "../ui/textarea.jsx";
import {
  customerTypes,
  customerStatuses,
  industries,
} from "../../lib/models/customer.js";
import cityService from "../../services/cities.service.js";
import companyCategoryService from "../../services/company-categories.service.js";

export function CustomerForm({ defaultValues, onSubmit, onCancel }) {
  const [cities, setCities] = useState([]);
  const [companyCategories, setCompanyCategories] = useState([]);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: defaultValues || {
      name: "",
      email: "",
      phone: "",
      address: "",
      cityId: "",
      state: "",
      country: "India",
      type: "",
      industry: "",
      companyCategoryId: "",
      website: "",
      status: "active",
    },
  });

  // Fetch cities and company categories on component mount
  useEffect(() => {
    const fetchCities = async () => {
      try {
        const citiesData = await cityService.getAllCities();
        const citiesArray = Array.isArray(citiesData.cities)
          ? citiesData.cities
          : [];
        setCities(citiesArray);
      } catch (error) {
        console.error("Error fetching cities:", error);
        setCities([]);
      }
    };

    const fetchCompanyCategories = async () => {
      try {
        const companyCategoriesData =
          await companyCategoryService.getAllCompanyCategories();
        const companyCategoriesArray = Array.isArray(
          companyCategoriesData.companyCategories
        )
          ? companyCategoriesData.companyCategories
          : [];
        setCompanyCategories(companyCategoriesArray);
      } catch (error) {
        console.error("Error fetching company categories:", error);
        setCompanyCategories([]);
      }
    };

    fetchCities();
    fetchCompanyCategories();
  }, []);

  const handleFormSubmit = async (data) => {
    console.log("Form data being submitted:", data);
    await onSubmit(data);
  };

  return (
    <div className="max-h-[70vh] overflow-y-auto pr-2">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Company Name *</Label>
            <Input
              id="name"
              {...register("name", { required: "Company name is required" })}
              placeholder="Enter company name"
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email *</Label>
            <Input
              id="email"
              type="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
              placeholder="Enter email address"
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone *</Label>
            <Input
              id="phone"
              {...register("phone", { required: "Phone number is required" })}
              placeholder="Enter phone number"
            />
            {errors.phone && (
              <p className="text-sm text-red-500">{errors.phone.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              {...register("website")}
              placeholder="https://example.com"
            />
            {errors.website && (
              <p className="text-sm text-red-500">{errors.website.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="gstNumber">GST Number</Label>
            <Input
              id="gstNumber"
              {...register("gstNumber")}
              placeholder="Enter GST number"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="industry">Industry *</Label>
            <Controller
              name="industry"
              control={control}
              rules={{ required: "Industry is required" }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map((industry) => (
                      <SelectItem key={industry} value={industry}>
                        {industry}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.industry && (
              <p className="text-sm text-red-500">{errors.industry.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyCategoryId">Company Category *</Label>
            <Controller
              name="companyCategoryId"
              control={control}
              rules={{ required: "Company category is required" }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company category" />
                  </SelectTrigger>
                  <SelectContent>
                    {companyCategories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.companyCategoryId && (
              <p className="text-sm text-red-500">
                {errors.companyCategoryId.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="cityId">City *</Label>
            <Controller
              name="cityId"
              control={control}
              rules={{ required: "City is required" }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select city" />
                  </SelectTrigger>
                  <SelectContent>
                    {cities.map((city) => (
                      <SelectItem key={city.id} value={city.id.toString()}>
                        {city.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.cityId && (
              <p className="text-sm text-red-500">{errors.cityId.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="state">State *</Label>
            <Input
              id="state"
              {...register("state", { required: "State is required" })}
              placeholder="Enter state"
            />
            {errors.state && (
              <p className="text-sm text-red-500">{errors.state.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="country">Country *</Label>
            <Input
              id="country"
              {...register("country", { required: "Country is required" })}
              placeholder="Enter country"
            />
            {errors.country && (
              <p className="text-sm text-red-500">{errors.country.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {customerStatuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Address</Label>
          <Textarea
            id="address"
            {...register("address")}
            placeholder="Enter full address"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="pincode">Pincode</Label>
            <Input
              id="pincode"
              {...register("pincode")}
              placeholder="Enter pincode"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="panNumber">PAN Number</Label>
            <Input
              id="panNumber"
              {...register("panNumber")}
              placeholder="Enter PAN number"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            {...register("notes")}
            placeholder="Enter any additional notes"
            rows={3}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : defaultValues ? "Save" : "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
