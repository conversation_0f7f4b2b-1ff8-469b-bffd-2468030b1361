export interface Stage {
  id: string
  name: string
  slug: string
  status: string
  createdAt: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  deletedBy?: number
  deletedAt?: string
}

export const sampleStages: Stage[] = [
  {
    id: "STAGE-001",
    name: "Inquiry",
    slug: "inquiry",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "STAGE-002", 
    name: "Lead",
    slug: "lead",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "STAGE-003",
    name: "Free Subscriber",
    slug: "free-subscriber", 
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "STAGE-004",
    name: "Attendee",
    slug: "attendee",
    status: "active", 
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: "STAGE-005",
    name: "<PERSON><PERSON> Customer",
    slug: "paid-customer",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
  },
];

export const createStage = (data: Partial<Stage>): Stage => {
  return {
    id: data.id || "",
    name: data.name || "",
    slug: data.slug || data.name?.toLowerCase().replace(/\s+/g, '-') || "",
    status: data.status || "active",
    createdAt: data.createdAt || new Date().toISOString(),
    updatedAt: data.updatedAt,
    createdBy: data.createdBy,
    updatedBy: data.updatedBy,
    deletedBy: data.deletedBy,
    deletedAt: data.deletedAt,
  };
};

export const getStageDisplayName = (stage: Stage): string => {
  return stage.name || stage.slug || "Unknown Stage";
};

export const isStageActive = (stage: Stage): boolean => {
  return stage.status === "active" && !stage.deletedAt;
};

export const generateSlugFromName = (name: string): string => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};
