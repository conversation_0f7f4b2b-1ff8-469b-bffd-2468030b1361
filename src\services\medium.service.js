import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class MediumService {
  async getAllMedia(params = { page: 1, limit: 100, search: "", status: "" }) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/mediums?${queryString}`
        : `${API_URL}/mediums`;

      const response = await axios.get(url);

      // Ensure data is in expected format and transform status to isActive
      const mediaData = Array.isArray(response.data) ? response.data : [];
      const transformedData = mediaData.map((item) => ({
        ...item,
        isActive: item.status === "active", // Convert status enum to boolean for frontend
      }));

      return {
        media: transformedData,
      };
    } catch (error) {
      console.error("Error fetching media:", error);
      throw error;
    }
  }

  async getMediumById(id) {
    try {
      const response = await axios.get(`${API_URL}/mediums/${id}`);
      const medium = response.data;

      // Transform status to isActive for frontend compatibility
      return {
        ...medium,
        isActive: medium.status === "active",
      };
    } catch (error) {
      console.error(`Error fetching medium with ID ${id}:`, error);
      throw error;
    }
  }

  async createMedium(mediumData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: mediumData.name,
        description: mediumData.description || "",
        status: mediumData.isActive ? "active" : "inactive", // Convert boolean to enum
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/mediums`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating medium:", error);
      throw error;
    }
  }

  async updateMedium(id, mediumData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: mediumData.name,
        description: mediumData.description || "",
        status: mediumData.isActive ? "active" : "inactive", // Convert boolean to enum
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/mediums/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating medium with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteMedium(id) {
    try {
      const response = await axios.delete(`${API_URL}/mediums/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting medium with ID ${id}:`, error);
      throw error;
    }
  }

  async bulkImportMediums(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/mediums/insert-mediums`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing mediums from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const mediumService = new MediumService();
export default mediumService;
