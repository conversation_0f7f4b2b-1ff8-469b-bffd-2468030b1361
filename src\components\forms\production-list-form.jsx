"use client";
import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import userService from "@/services/user.service";
import roleService from "@/services/roles.service";

export function ProductionListForm({ onSubmit, onCancel, defaultValues = {} }) {
  const [operators, setOperators] = useState([]);
  const [isLoadingOperators, setIsLoadingOperators] = useState(true);
  const [roles, setRoles] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [appointmentDate, setAppointmentDate] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      status: "pending",
      vehicleType: "",
      operatorRole: "",
      ...defaultValues,
    },
  });

  const status = watch("status");
  const vehicleType = watch("vehicleType");
  const operatorId = watch("operatorId");
  const operatorRole = watch("operatorRole");

  // Fetch operators and roles, get current user
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoadingOperators(true);

        // Fetch operators
        const operatorsResult = await userService.getAllUsers({ limit: 1000 });
        const operatorsList = Array.isArray(operatorsResult)
          ? operatorsResult
          : [];
        setOperators(operatorsList);

        // Fetch roles
        const rolesResult = await roleService.getRolesForDropdown();
        setRoles(rolesResult);

        // Get current user from session storage
        const userSession = sessionStorage.getItem("user");
        if (userSession) {
          setCurrentUser(JSON.parse(userSession));
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setOperators([]);
        setRoles([]);
      } finally {
        setIsLoadingOperators(false);
      }
    };

    fetchData();
  }, []);

  // Handle default values and date parsing
  useEffect(() => {
    if (defaultValues.appointmentDate) {
      // Parse the date string (format: "2002-10-10") to Date object
      const dateValue = new Date(defaultValues.appointmentDate);
      setAppointmentDate(dateValue);
    }
  }, [defaultValues]);

  const handleFormSubmit = (data) => {
    // Format the appointment date to YYYY-MM-DD format for backend
    const formattedData = {
      ...data,
      appointmentDate: appointmentDate
        ? appointmentDate.toISOString().split("T")[0]
        : null,
    };
    onSubmit(formattedData);
  };

  const vehicleTypes = [
    "Car",
    "Truck",
    "Van",
    "Motorcycle",
    "Bus",
    "SUV",
    "Pickup",
    "Other",
  ];

  const statusOptions = [
    { value: "pending", label: "Pending" },
    { value: "in-progress", label: "In Progress" },
    { value: "completed", label: "Completed" },
    { value: "cancelled", label: "Cancelled" },
  ];

  return (
    <div className="w-full max-w-3xl mx-auto p-4 overflow-y-auto max-h-[90vh]">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="productionSheetNo">Production Sheet No *</Label>
          <Input
            id="productionSheetNo"
            {...register("productionSheetNo", {
              required: "Production sheet number is required",
            })}
            placeholder="Enter production sheet number"
          />
          {errors.productionSheetNo && (
            <p className="text-sm text-red-500">
              {errors.productionSheetNo.message}
            </p>
          )}
        </div>

        {/* Role-based quantity fields */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="operatorQuantity">Operator Quantity</Label>
            <Input
              id="operatorQuantity"
              type="number"
              min="0"
              {...register("operatorQuantity")}
              placeholder="Enter operator quantity"
              disabled={currentUser?.role !== "Operator"}
            />
            {errors.operatorQuantity && (
              <p className="text-sm text-red-500">
                {errors.operatorQuantity.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="packagerQuantity">Packager Quantity</Label>
            <Input
              id="packagerQuantity"
              type="number"
              min="0"
              {...register("packagerQuantity")}
              placeholder="Enter packager quantity"
              disabled={currentUser?.role !== "Packager"}
            />
            {errors.packagerQuantity && (
              <p className="text-sm text-red-500">
                {errors.packagerQuantity.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="quantity">Total Quantity *</Label>
          <Input
            id="quantity"
            type="number"
            min="1"
            {...register("quantity", {
              required: "Total quantity is required",
              min: { value: 1, message: "Total quantity must be at least 1" },
            })}
            placeholder="Enter total quantity"
          />
          {errors.quantity && (
            <p className="text-sm text-red-500">{errors.quantity.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="operatorId">Operator *</Label>
          <Select
            value={operatorId?.toString() || ""}
            onValueChange={(value) => setValue("operatorId", parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  isLoadingOperators
                    ? "Loading operators..."
                    : "Select an operator"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {operators.map((operator) => (
                <SelectItem key={operator.id} value={operator.id.toString()}>
                  {operator.name} ({operator.email})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.operatorId && (
            <p className="text-sm text-red-500">{errors.operatorId.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="operatorRole">Operator Role *</Label>
          <Select
            value={operatorRole}
            onValueChange={(value) => setValue("operatorRole", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select operator role" />
            </SelectTrigger>
            <SelectContent>
              {roles
                .filter((role) => role.status === "active")
                .map((role) => (
                  <SelectItem key={role.id} value={role.name}>
                    {role.name}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          {errors.operatorRole && (
            <p className="text-sm text-red-500">
              {errors.operatorRole.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="vehicleType">Vehicle Type *</Label>
          <Select
            value={vehicleType}
            onValueChange={(value) => setValue("vehicleType", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select vehicle type" />
            </SelectTrigger>
            <SelectContent>
              {vehicleTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.vehicleType && (
            <p className="text-sm text-red-500">{errors.vehicleType.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="appointmentDate">Appointment Date *</Label>
          <DatePicker
            value={appointmentDate}
            onChange={setAppointmentDate}
            placeholder="Select appointment date"
            className="text-black [&_.react-datepicker__navigation-icon]:border-gray-700"
          />
          {!appointmentDate && (
            <p className="text-sm text-red-500">Appointment date is required</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={status}
            onValueChange={(value) => setValue("status", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            {...register("notes")}
            placeholder="Enter any additional notes"
            rows={3}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save Production List</Button>
        </div>
      </form>
    </div>
  );
}
