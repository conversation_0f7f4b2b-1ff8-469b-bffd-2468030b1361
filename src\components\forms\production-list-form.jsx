"use client";
import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import userService from "@/services/user.service";

export function ProductionListForm({ onSubmit, onCancel, defaultValues = {} }) {
  const [operators, setOperators] = useState([]);
  const [isLoadingOperators, setIsLoadingOperators] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      status: "pending",
      vehicleType: "",
      ...defaultValues,
    },
  });

  const status = watch("status");
  const vehicleType = watch("vehicleType");
  const operatorId = watch("operatorId");

  // Fetch operators for the dropdown
  useEffect(() => {
    const fetchOperators = async () => {
      try {
        setIsLoadingOperators(true);
        const result = await userService.getAllUsers({ limit: 1000 });
        const operatorsList = Array.isArray(result.users) ? result.users : [];
        setOperators(operatorsList);
      } catch (error) {
        console.error("Error fetching operators:", error);
        setOperators([]);
      } finally {
        setIsLoadingOperators(false);
      }
    };

    fetchOperators();
  }, []);

  const handleFormSubmit = (data) => {
    onSubmit(data);
  };

  const vehicleTypes = [
    "Car",
    "Truck",
    "Van",
    "Motorcycle",
    "Bus",
    "SUV",
    "Pickup",
    "Other"
  ];

  const statusOptions = [
    { value: "pending", label: "Pending" },
    { value: "in-progress", label: "In Progress" },
    { value: "completed", label: "Completed" },
    { value: "cancelled", label: "Cancelled" },
  ];

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="productionSheetNo">Production Sheet No *</Label>
        <Input
          id="productionSheetNo"
          {...register("productionSheetNo", { 
            required: "Production sheet number is required" 
          })}
          placeholder="Enter production sheet number"
        />
        {errors.productionSheetNo && (
          <p className="text-sm text-red-500">{errors.productionSheetNo.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="quantity">Quantity *</Label>
        <Input
          id="quantity"
          type="number"
          min="1"
          {...register("quantity", { 
            required: "Quantity is required",
            min: { value: 1, message: "Quantity must be at least 1" }
          })}
          placeholder="Enter quantity"
        />
        {errors.quantity && (
          <p className="text-sm text-red-500">{errors.quantity.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="operatorId">Operator *</Label>
        <Select
          value={operatorId?.toString() || ""}
          onValueChange={(value) => setValue("operatorId", parseInt(value))}
        >
          <SelectTrigger>
            <SelectValue placeholder={isLoadingOperators ? "Loading operators..." : "Select an operator"} />
          </SelectTrigger>
          <SelectContent>
            {operators.map((operator) => (
              <SelectItem key={operator.id} value={operator.id.toString()}>
                {operator.name} ({operator.email})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.operatorId && (
          <p className="text-sm text-red-500">{errors.operatorId.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="vehicleType">Vehicle Type *</Label>
        <Select
          value={vehicleType}
          onValueChange={(value) => setValue("vehicleType", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select vehicle type" />
          </SelectTrigger>
          <SelectContent>
            {vehicleTypes.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.vehicleType && (
          <p className="text-sm text-red-500">{errors.vehicleType.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="appointmentDate">Appointment Date *</Label>
        <Input
          id="appointmentDate"
          type="datetime-local"
          {...register("appointmentDate", { 
            required: "Appointment date is required" 
          })}
        />
        {errors.appointmentDate && (
          <p className="text-sm text-red-500">{errors.appointmentDate.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">Status</Label>
        <Select
          value={status}
          onValueChange={(value) => setValue("status", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          {...register("notes")}
          placeholder="Enter any additional notes"
          rows={3}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Save Production List</Button>
      </div>
    </form>
  );
}
