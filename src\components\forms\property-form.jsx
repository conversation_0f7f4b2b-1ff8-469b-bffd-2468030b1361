"use client";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function PropertyForm({
  onSubmit,
  onCancel,
  defaultValues = {},
  channels = [],
  mediums = [],
}) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      isActive: true,
      currency: "INR",
      mediumId: "",
      channelId: "",
      ...defaultValues,
    },
  });

  // Add validation for required fields
  const validateForm = (data) => {
    const newErrors = {};
    if (!data.name) newErrors.name = "Property name is required";
    if (!data.mediumId) newErrors.mediumId = "Medium is required";
    if (!data.channelId) newErrors.channelId = "Channel is required";
    return newErrors;
  };

  const isActive = watch("isActive");

  const handleFormSubmit = (data) => {
    const validationErrors = validateForm(data);
    if (Object.keys(validationErrors).length > 0) {
      // Set errors manually if validation fails
      Object.keys(validationErrors).forEach((key) => {
        setValue(key, data[key], { shouldValidate: true });
      });
      return;
    }
    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Property Name *</Label>
        <Input
          id="name"
          {...register("name", { required: "Property name is required" })}
          placeholder="Enter property name"
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="mediumId">Medium *</Label>
          <Select
            value={watch("mediumId") || ""}
            onValueChange={(value) =>
              setValue("mediumId", value, { shouldValidate: true })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select medium" />
            </SelectTrigger>
            <SelectContent>
              {mediums.map((medium) => (
                <SelectItem key={medium.id} value={medium.id}>
                  {medium.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.mediumId && (
            <p className="text-sm text-red-500">{errors.mediumId.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="channelId">Channel *</Label>
          <Select
            value={watch("channelId") || ""}
            onValueChange={(value) =>
              setValue("channelId", value, { shouldValidate: true })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select channel" />
            </SelectTrigger>
            <SelectContent>
              {channels.map((channel) => (
                <SelectItem key={channel.id} value={channel.id}>
                  {channel.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.channelId && (
            <p className="text-sm text-red-500">{errors.channelId.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          {...register("description")}
          placeholder="Enter property description"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="price">Price</Label>
          <Input
            id="price"
            type="number"
            step="0.01"
            {...register("price")}
            placeholder="Enter price"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select
            value={watch("currency") || "INR"}
            onValueChange={(value) =>
              setValue("currency", value, { shouldValidate: true })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="INR">INR</SelectItem>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="GBP">GBP</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={isActive}
          onCheckedChange={(checked) => setValue("isActive", checked)}
        />
        <Label htmlFor="isActive">Active</Label>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Save Property</Button>
      </div>
    </form>
  );
}
