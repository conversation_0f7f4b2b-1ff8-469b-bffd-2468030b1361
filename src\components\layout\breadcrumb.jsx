import { useLocation, Link } from "react-router-dom"
import { ChevronRight, Home } from "lucide-react"
import {
  BreadcrumbComponent,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../ui/breadcrumb.jsx"

// Changed from CustomBreadcrumb to Breadcrumb to match imports
export function Breadcrumb() {
  const location = useLocation()
  const pathnames = location.pathname.split("/").filter((x) => x)

  const breadcrumbNameMap = {
    customers: "Customers",
    analytics: "Analytics",
    insights: "Insights",
    reports: "Reports",
    settings: "Settings",
    documentation: "Documentation",
    targeting: "Targeting",
    demographics: "Demographics",
    "events-reporting": "Events Reporting",
    "system-structure": "System Structure",
    convention: "Convention",
    transactions: "Transactions",
    users: "Users",
    "database-search": "Database Search",
    advertising: "Advertising",
    awards: "Awards",
    "customer-journey": "Customer Journey",
    add: "Add",
    events: "Events",
    revenue: "Revenue",
    "revenue-tracking": "Revenue Tracking",
    masters: "Data Management",
    variants: "Variants",
    medium: "Medium",
    properties: "Properties",
    cities: "Cities",
    companies: "Companies",
    channels: "Channels",
  }

  return (
    <BreadcrumbComponent>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to="/">
              <Home className="h-4 w-4" />
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {pathnames.map((name, index) => {
          const routeTo = `/${pathnames.slice(0, index + 1).join("/")}`
          const isLast = index === pathnames.length - 1
          const displayName = breadcrumbNameMap[name] || name

          return (
            <div key={name} className="flex items-center">
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                {isLast ? (
                  <BreadcrumbPage>{displayName}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link to={routeTo}>{displayName}</Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </div>
          )
        })}
      </BreadcrumbList>
    </BreadcrumbComponent>
  )
}
