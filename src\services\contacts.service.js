import axios from "axios";
const API_URL = "http://localhost:4000/api";

class ContactsService {
  async getAllContacts(
    params = { page: 1, limit: 100, search: "", status: "" }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/contacts?${queryString}`
        : `${API_URL}/contacts`;

      const response = await axios.get(url);

      // Handle different response formats
      if (response.data && response.data.contacts) {
        return {
          contacts: response.data.contacts,
          total: response.data.total || response.data.contacts.length,
          page: response.data.page || 1,
          limit: response.data.limit || 100,
        };
      } else if (Array.isArray(response.data)) {
        return {
          contacts: response.data,
          total: response.data.length,
          page: 1,
          limit: 100,
        };
      } else {
        console.warn("Unexpected response format:", response.data);
        return {
          contacts: [],
          total: 0,
          page: 1,
          limit: 100,
        };
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
      throw error;
    }
  }

  async getContactById(id) {
    try {
      const response = await axios.get(`${API_URL}/contacts/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching contact with ID ${id}:`, error);
      throw error;
    }
  }

  async createContact(contactData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: contactData.name,
        email: contactData.email,
        phone: contactData.phone || null,
        company: contactData.company || null,
        message: contactData.message || null,
        status: contactData.status || "new",
        priority: contactData.priority || "medium",
        assignedTo: contactData.assignedTo || null,
        tags: contactData.tags || null,
        notes: contactData.notes || null,
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/contacts`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating contact:", error);
      throw error;
    }
  }

  async updateContact(id, contactData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: contactData.name,
        email: contactData.email,
        phone: contactData.phone || null,
        company: contactData.company || null,
        message: contactData.message || null,
        status: contactData.status || "new",
        priority: contactData.priority || "medium",
        assignedTo: contactData.assignedTo || null,
        tags: contactData.tags || null,
        notes: contactData.notes || null,
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/contacts/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating contact with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteContact(id) {
    try {
      const response = await axios.delete(`${API_URL}/contacts/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting contact with ID ${id}:`, error);
      throw error;
    }
  }

  // Helper method to get users for assignedTo dropdown
  async getUsers() {
    try {
      const response = await axios.get(`${API_URL}/users`);
      return Array.isArray(response.data)
        ? response.data
        : response.data.users || [];
    } catch (error) {
      console.error("Error fetching users:", error);
      return [];
    }
  }

  async bulkImportContacts(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/contacts/insert-contacts`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing contacts from Excel:", error);
      throw error;
    }
  }
}

const contactsService = new ContactsService();
export default contactsService;
