// Local storage utilities for React.js

const STORAGE_KEYS = {
  AUTH_TOKEN: "auth_token",
  USER_DATA: "user_data",
  THEME: "theme",
  PREFERENCES: "user_preferences",
  CACHE: "api_cache",
}

class StorageService {
  // Get item from localStorage
  get(key) {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error(`Error getting item from storage: ${key}`, error)
      return null
    }
  }

  // Set item in localStorage
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error(`Error setting item in storage: ${key}`, error)
      return false
    }
  }

  // Remove item from localStorage
  remove(key) {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error(`Error removing item from storage: ${key}`, error)
      return false
    }
  }

  // Clear all localStorage
  clear() {
    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.error("Error clearing storage", error)
      return false
    }
  }

  // Check if item exists
  has(key) {
    return localStorage.getItem(key) !== null
  }

  // Auth specific methods
  auth = {
    getToken: () => this.get(STORAGE_KEYS.AUTH_TOKEN),
    setToken: (token) => this.set(STORAGE_KEYS.AUTH_TOKEN, token),
    removeToken: () => this.remove(STORAGE_KEYS.AUTH_TOKEN),
    getUser: () => this.get(STORAGE_KEYS.USER_DATA),
    setUser: (user) => this.set(STORAGE_KEYS.USER_DATA, user),
    removeUser: () => this.remove(STORAGE_KEYS.USER_DATA),
    clearAuth: () => {
      this.remove(STORAGE_KEYS.AUTH_TOKEN)
      this.remove(STORAGE_KEYS.USER_DATA)
    },
  }

  // Theme methods
  theme = {
    get: () => this.get(STORAGE_KEYS.THEME) || "light",
    set: (theme) => this.set(STORAGE_KEYS.THEME, theme),
  }

  // Preferences methods
  preferences = {
    get: () => this.get(STORAGE_KEYS.PREFERENCES) || {},
    set: (preferences) => this.set(STORAGE_KEYS.PREFERENCES, preferences),
    update: (key, value) => {
      const current = this.get() || {}
      return this.set({ ...current, [key]: value })
    },
  }

  // Cache methods
  cache = {
    get: (key) => {
      const cache = this.get(STORAGE_KEYS.CACHE) || {}
      const item = cache[key]

      if (!item) return null

      // Check if cache item has expired
      if (item.expiry && Date.now() > item.expiry) {
        this.remove(key)
        return null
      }

      return item.data
    },
    set: (key, data, ttl = 3600000) => {
      // Default 1 hour TTL
      const cache = this.get(STORAGE_KEYS.CACHE) || {}
      cache[key] = {
        data,
        expiry: ttl ? Date.now() + ttl : null,
        timestamp: Date.now(),
      }
      return this.set(STORAGE_KEYS.CACHE, cache)
    },
    remove: (key) => {
      const cache = this.get(STORAGE_KEYS.CACHE) || {}
      delete cache[key]
      return this.set(STORAGE_KEYS.CACHE, cache)
    },
    clear: () => this.set(STORAGE_KEYS.CACHE, {}),
  }
}

export const storage = new StorageService()
export { STORAGE_KEYS }
export default storage
