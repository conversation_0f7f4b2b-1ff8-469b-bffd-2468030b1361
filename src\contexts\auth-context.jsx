"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import { apiService } from "../lib/services/api.js"

const AuthContext = createContext(undefined)

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const sessionValues = sessionStorage.getItem("user")
      if (sessionValues) {
        setUser(JSON.parse(sessionValues))
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email, password) => {
    setIsLoading(true)
    try {
      const response = await apiService.auth.login(email, password)
        console.log("res11111",response);
      if (response.status) {
        sessionStorage.setItem("user", JSON.stringify(response?.result?.user))
        sessionStorage.setItem('token',JSON.stringify(response?.result?.token));
        setUser(response?.result?.user)
        return { success: true }
      }

      return {
        success: false,
        message: response.message || "Invalid email or password",
      }
    } catch (error) {
      console.error("Login error:", error)
      return {
        success: false,
        message: error.message || "An error occurred during login",
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      sessionStorage.clear()
      setUser(null)
      navigate("/login")
    } catch (error) {
      console.error("Logout error:", error)
      setUser(null)
      navigate("/login")
    } finally {
      setIsLoading(false)
    }
  }

  return <AuthContext.Provider value={{ user, isLoading, login, logout }}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
