import {
  LineChart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
} from "recharts"
import { cn } from "@/lib/utils"

export function LineChart({
  data,
  categories,
  index,
  className,
  colors = ["#3b82f6", "#ef4444", "#10b981", "#f59e0b", "#8b5cf6"],
  valueF<PERSON><PERSON>er,
  yAxisWidth = 56,
  ...props
}) {
  const categoryKeys = Object.keys(categories || {})

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }} {...props}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={index} />
          <YAxis width={yAxisWidth} tickFormatter={valueF<PERSON><PERSON><PERSON>} />
          <Tooltip formatter={valueFormatter} />
          <Legend />
          {categoryKeys.map((key, idx) => (
            <Line
              key={key}
              type="monotone"
              dataKey={key}
              stroke={colors[idx % colors.length]}
              strokeWidth={2}
              name={categories[key]?.label || key}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  )
}
