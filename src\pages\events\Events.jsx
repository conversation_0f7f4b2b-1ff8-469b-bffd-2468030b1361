"use client"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Calendar, Users, Plus, TrendingUp } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Link } from "react-router-dom"

const eventsData = [
  {
    id: "EVT-001",
    name: "Digital Marketing Summit 2024",
    type: "Conference",
    date: "2024-03-15",
    location: "Mumbai, India",
    attendees: 450,
    status: "Upcoming",
    revenue: 125000,
  },
  {
    id: "EVT-002",
    name: "AI in Business Webinar",
    type: "Webinar",
    date: "2024-02-20",
    location: "Online",
    attendees: 320,
    status: "Completed",
    revenue: 45000,
  },
  {
    id: "EVT-003",
    name: "Tech Innovation Expo",
    type: "Exhibition",
    date: "2024-04-10",
    location: "Bangalore, India",
    attendees: 1200,
    status: "Upcoming",
    revenue: 280000,
  },
]

const monthlyEvents = [
  { month: "Jan", events: 5, attendees: 1200 },
  { month: "Feb", events: 8, attendees: 1800 },
  { month: "Mar", events: 6, attendees: 1500 },
  { month: "Apr", events: 10, attendees: 2200 },
  { month: "May", events: 7, attendees: 1600 },
  { month: "Jun", events: 9, attendees: 2000 },
]

export default function EventsPage() {
  const totalEvents = eventsData.length
  const upcomingEvents = eventsData.filter((e) => e.status === "Upcoming").length
  const totalAttendees = eventsData.reduce((sum, e) => sum + e.attendees, 0)
  const totalRevenue = eventsData.reduce((sum, e) => sum + e.revenue, 0)

  const getStatusColor = (status) => {
    switch (status) {
      case "Upcoming":
        return "bg-blue-100 text-blue-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      case "Cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeColor = (type) => {
    switch (type) {
      case "Conference":
        return "bg-purple-100 text-purple-800"
      case "Webinar":
        return "bg-blue-100 text-blue-800"
      case "Exhibition":
        return "bg-green-100 text-green-800"
      case "Workshop":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Events</h1>
          <p className="text-muted-foreground">Manage and track all events and conferences</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Event
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEvents}</div>
            <p className="text-xs text-muted-foreground">All events</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{upcomingEvents}</div>
            <p className="text-xs text-muted-foreground">Scheduled events</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Attendees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAttendees.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Expected attendance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(totalRevenue / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">Revenue generated</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="events" className="space-y-6">
        <TabsList>
          <TabsTrigger value="events">All Events</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {eventsData.map((event) => (
              <Card key={event.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{event.name}</CardTitle>
                    <Badge className={getStatusColor(event.status)}>{event.status}</Badge>
                  </div>
                  <CardDescription>{event.id}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Type:</span>
                      <Badge className={getTypeColor(event.type)}>{event.type}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Date:</span>
                      <span className="text-sm font-medium">{event.date}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Location:</span>
                      <span className="text-sm">{event.location}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Attendees:</span>
                      <span className="text-sm font-medium">{event.attendees}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Revenue:</span>
                      <span className="text-sm font-medium">${event.revenue.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t flex space-x-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      View Details
                    </Button>
                    <Link to="/events/convention" className="flex-1">
                      <Button size="sm" className="w-full">
                        Manage
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Event Analytics</CardTitle>
              <CardDescription>Events and attendance over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyEvents}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="events" fill="#8884d8" name="Events" />
                  <Bar dataKey="attendees" fill="#82ca9d" name="Attendees" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Event Calendar</CardTitle>
              <CardDescription>Calendar view of all events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <div className="text-lg text-muted-foreground">Event Calendar</div>
                <p className="text-sm text-muted-foreground mt-2">Calendar integration coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
