"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Upload,
  Download,
  FileUp,
  Check,
  AlertCircle,
  FileSpreadsheet,
  FileX,
} from "lucide-react";

import { But<PERSON> } from "./ui/button.jsx";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog.jsx";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./ui/form.jsx";
import { Input } from "./ui/input.jsx";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert.jsx";
import { Progress } from "./ui/progress.jsx";

export function DataImportExport({
  entityName = "Data",
  onImport,
  onExport,
  templateUrl,
  templatePath,
  acceptedFileTypes = ".csv",
  fileDescription,
}) {
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [importStatus, setImportStatus] = useState("idle");
  const [importProgress, setImportProgress] = useState(0);
  const [importMessage, setImportMessage] = useState("");
  const [importResults, setImportResults] = useState(null);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);

  const form = useForm({
    defaultValues: {
      file: undefined,
      type: entityName,
    },
  });

  // Function to reset all states and form
  const resetImportState = () => {
    setImportStatus("idle");
    setImportProgress(0);
    setImportMessage("");
    setImportResults(null);
    setIsImporting(false);
    form.reset({
      file: undefined,
      type: entityName,
    });
  };

  // Handle dialog close
  const handleDialogClose = (open) => {
    setIsImportDialogOpen(open);
    if (!open) {
      // Reset everything when dialog closes
      resetImportState();
    }
  };

  // Handle downloading rejected file
  const handleDownloadRejectedFile = (e, rejectedFileUrl) => {
    e.preventDefault(); // Prevent any form submission
    e.stopPropagation(); // Stop event bubbling

    if (!rejectedFileUrl) return;

    const baseUrl = "http://localhost:4000";
    // Ensure the rejectedFileUrl starts with / for proper URL construction
    const cleanPath = rejectedFileUrl.startsWith("/")
      ? rejectedFileUrl
      : `/${rejectedFileUrl}`;
    const fullUrl = `${baseUrl}${cleanPath}`;

    // Open in new tab to download
    window.open(fullUrl, "_blank");
  };

  const handleImport = async (values) => {
    if (!onImport) return;

    try {
      setIsImporting(true);
      setImportStatus("processing");
      setImportProgress(0);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 300);

      const result = await onImport(values.file);

      clearInterval(progressInterval);
      setImportProgress(100);
      setImportStatus("success");

      // Store detailed results if available
      if (result && result.data) {
        setImportResults(result.data);
        setImportMessage(
          result.data.summary?.message ||
            `Successfully imported ${entityName || "data"}`
        );
      } else if (
        result &&
        (result.totalRows || result.insertedCount !== undefined)
      ) {
        // If result is the data itself (not wrapped in data property)
        setImportResults(result);
        setImportMessage(
          result.summary?.message ||
            `Successfully imported ${entityName || "data"}`
        );
      } else {
        setImportMessage(`Successfully imported ${entityName || "data"}`);
      }

      // Reset form after successful import
      setTimeout(() => {
        form.reset();
      }, 2000);
    } catch (error) {
      setImportStatus("error");
      setImportMessage(
        error instanceof Error
          ? error.message
          : `Failed to import ${entityName || "data"}`
      );
    } finally {
      setIsImporting(false);
    }
  };

  const handleExport = async () => {
    if (!onExport) return;
    try {
      setIsExporting(true);
      await onExport(); // uses table.getSortedRowModel().rows from above
    } catch (error) {
      console.error("Export failed:", error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Dialog open={isImportDialogOpen} onOpenChange={handleDialogClose}>
        <DialogTrigger asChild>
          {!["Company Categories", "Stages"].includes(entityName) && (
            <Button variant="outline" size="sm">
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileSpreadsheet className="h-5 w-5" />
              Import {entityName} Data
            </DialogTitle>
            <DialogDescription>
              Upload a file to bulk import {entityName?.toLowerCase() || "data"}
              .
              {fileDescription && (
                <span className="block mt-1 text-sm">{fileDescription}</span>
              )}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleImport)}
              className="space-y-4"
            >
              {importStatus === "idle" || importStatus === "processing" ? (
                <>
                  <FormField
                    control={form.control}
                    name="file"
                    render={({ field: { onChange, value, ...field } }) => (
                      <FormItem>
                        <FormLabel className="text-base font-medium">
                          Select File to Import
                        </FormLabel>
                        <FormControl>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                            <Input
                              {...field}
                              type="file"
                              accept={acceptedFileTypes}
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  onChange(file);
                                }
                              }}
                              disabled={isImporting}
                              className="hidden"
                              id="file-upload"
                            />
                            <label
                              htmlFor="file-upload"
                              className="cursor-pointer flex flex-col items-center gap-2"
                            >
                              <FileSpreadsheet className="h-8 w-8 text-gray-400" />
                              <span className="text-sm font-medium text-gray-700">
                                Click to select file or drag and drop
                              </span>
                              <span className="text-xs text-gray-500">
                                Supported formats:{" "}
                                {acceptedFileTypes
                                  .replace(/\./g, "")
                                  .toUpperCase()}
                              </span>
                            </label>
                          </div>
                        </FormControl>
                        <FormDescription className="text-center">
                          {value && (
                            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
                              Selected: {value.name} (
                              {(value.size / 1024).toFixed(1)} KB)
                            </div>
                          )}
                          {(templatePath || templateUrl) && (
                            <div className="mt-3">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const link = document.createElement("a");
                                  link.href = templatePath || templateUrl;
                                  link.download = `${entityName}_Template.xlsx`;
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                              >
                                <Download className="mr-2 h-4 w-4" />
                                Download Template
                              </Button>
                            </div>
                          )}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {importStatus === "processing" && (
                    <div className="space-y-2">
                      <Progress value={importProgress} className="h-2" />
                      <p className="text-sm text-muted-foreground text-center">
                        Processing... {importProgress}%
                      </p>
                    </div>
                  )}

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        resetImportState();
                        setIsImportDialogOpen(false);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isImporting}>
                      {isImporting ? (
                        <>
                          <FileUp className="mr-2 h-4 w-4 animate-bounce" />
                          Importing...
                        </>
                      ) : (
                        <>
                          <FileUp className="mr-2 h-4 w-4" />
                          Import
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </>
              ) : importStatus === "success" ? (
                <div className="space-y-4">
                  <Alert
                    variant="default"
                    className="bg-green-50 border-green-200"
                  >
                    <Check className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-800">
                      Import Completed
                    </AlertTitle>
                    <AlertDescription className="text-green-700">
                      {importMessage}
                    </AlertDescription>
                  </Alert>

                  {/* Detailed Results */}
                  {importResults && (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {/* Summary Stats */}
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div className="bg-green-100 p-2 rounded text-center">
                          <div className="font-semibold text-green-800">
                            {importResults.insertedCount}
                          </div>
                          <div className="text-green-600">Inserted</div>
                        </div>
                        <div className="bg-yellow-100 p-2 rounded text-center">
                          <div className="font-semibold text-yellow-800">
                            {importResults.skippedCount}
                          </div>
                          <div className="text-yellow-600">Skipped</div>
                        </div>
                        <div className="bg-red-100 p-2 rounded text-center">
                          <div className="font-semibold text-red-800">
                            {importResults.errorCount}
                          </div>
                          <div className="text-red-600">Errors</div>
                        </div>
                      </div>

                      {/* Download Rejected File Button */}
                      {importResults.rejectedFileUrl && (
                        <div className="flex justify-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) =>
                              handleDownloadRejectedFile(
                                e,
                                importResults.rejectedFileUrl
                              )
                            }
                            className="border-red-300 text-red-700 hover:bg-red-50"
                          >
                            <FileX className="mr-2 h-4 w-4" />
                            Download Rejected Rows
                          </Button>
                        </div>
                      )}

                      {/* Skipped Items - Collapsible */}
                      {/* {importResults.skippedData &&
                        importResults.skippedData.length > 0 && (
                          <Collapsible
                            open={isSkippedOpen}
                            onOpenChange={setIsSkippedOpen}
                          >
                            <div className="bg-yellow-50 border border-yellow-200 rounded">
                              <CollapsibleTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="w-full justify-between p-3 h-auto hover:bg-yellow-100"
                                >
                                  <span className="font-medium text-yellow-800">
                                    Skipped Items (
                                    {importResults.skippedData.length})
                                  </span>
                                  {isSkippedOpen ? (
                                    <ChevronDown className="h-4 w-4 text-yellow-600" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4 text-yellow-600" />
                                  )}
                                </Button>
                              </CollapsibleTrigger>
                              <CollapsibleContent className="px-3 pb-3">
                                <div className="space-y-1 max-h-32 overflow-y-auto">
                                  {importResults.skippedData.map(
                                    (item, index) => (
                                      <div
                                        key={index}
                                        className="text-xs text-yellow-700 bg-yellow-100 p-1 rounded"
                                      >
                                        Row {item.row}: {item.name} -{" "}
                                        {item.reason}
                                      </div>
                                    )
                                  )}
                                </div>
                              </CollapsibleContent>
                            </div>
                          </Collapsible>
                        )} */}

                      {/* Error Items - Collapsible */}
                      {/* {importResults.errors &&
                        importResults.errors.length > 0 && (
                          <Collapsible
                            open={isErrorsOpen}
                            onOpenChange={setIsErrorsOpen}
                          >
                            <div className="bg-red-50 border border-red-200 rounded">
                              <CollapsibleTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="w-full justify-between p-3 h-auto hover:bg-red-100"
                                >
                                  <span className="font-medium text-red-800">
                                    Errors ({importResults.errors.length})
                                  </span>
                                  {isErrorsOpen ? (
                                    <ChevronDown className="h-4 w-4 text-red-600" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4 text-red-600" />
                                  )}
                                </Button>
                              </CollapsibleTrigger>
                              <CollapsibleContent className="px-3 pb-3">
                                <div className="space-y-1 max-h-32 overflow-y-auto">
                                  {importResults.errors.map((error, index) => (
                                    <div
                                      key={index}
                                      className="text-xs text-red-700 bg-red-100 p-1 rounded"
                                    >
                                      Row {error.row}: {error.reason}
                                    </div>
                                  ))}
                                </div>
                              </CollapsibleContent>
                            </div>
                          </Collapsible>
                        )} */}
                    </div>
                  )}

                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => {
                      resetImportState();
                      setIsImportDialogOpen(false);
                    }}
                  >
                    Close
                  </Button>
                </div>
              ) : (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{importMessage}</AlertDescription>
                  <Button
                    className="mt-4"
                    variant="destructive"
                    onClick={() => {
                      resetImportState();
                    }}
                  >
                    Try Again
                  </Button>
                </Alert>
              )}
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <Button
        variant="outline"
        size="sm"
        onClick={handleExport}
        // disabled={isExporting || !onExport}
      >
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>
  );
}
