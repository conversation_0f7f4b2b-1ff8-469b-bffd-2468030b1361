import { Menu } from "lucide-react"
import { But<PERSON> } from "../ui/button.jsx"

export function MobileHeader() {
  return (
    <div className="flex h-14 items-center px-4 lg:hidden">
      <Button variant="ghost" size="icon" className="lg:hidden">
        <Menu className="h-6 w-6" />
        <span className="sr-only">Toggle navigation menu</span>
      </Button>
      <div className="flex-1">
        <h1 className="text-lg font-semibold">M4G Business Intelligence</h1>
      </div>
    </div>
  )
}
