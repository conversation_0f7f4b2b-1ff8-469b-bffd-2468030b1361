import { type Customer, sampleCustomers } from "@/lib/models/customer"
import {
  type Medium,
  type Channel,
  type Property,
  type Variant,
  sampleMediums,
  sampleChannels,
  sampleProperties,
  sampleVariants,
} from "@/lib/models/masterData"
import { type Transaction, sampleTransactions } from "@/lib/models/transaction"
import { type User, sampleUsers } from "@/lib/models/user"
import { type Company, sampleCompanies } from "@/lib/models/company"
import { type City, sampleCities } from "@/lib/models/city"
import { type CustomerJourney, sampleCustomerJourneys } from "@/lib/models/customerJourney"

// In-memory database simulation
class InMemoryDatabase {
  private customers: Customer[] = [...sampleCustomers]
  private mediums: Medium[] = [...sampleMediums]
  private channels: Channel[] = [...sampleChannels]
  private properties: Property[] = [...sampleProperties]
  private variants: Variant[] = [...sampleVariants]
  private transactions: Transaction[] = [...sampleTransactions]
  private users: User[] = [...sampleUsers]
  private companies: Company[] = [...sampleCompanies]
  private cities: City[] = [...sampleCities]
  private customerJourneys: CustomerJourney[] = [...sampleCustomerJourneys]

  // Generic CRUD operations
  getAll<T>(collection: string): T[] {
    return (this as any)[collection] || []
  }

  getById<T>(collection: string, id: string): T | undefined {
    const items = this.getAll<T>(collection)
    return items.find((item: any) => item.id === id)
  }

  create<T extends { id: string }>(collection: string, item: T): T {
    const items = (this as any)[collection]
    if (!items) throw new Error(`Collection ${collection} not found`)

    items.push(item)
    return item
  }

  update<T extends { id: string }>(collection: string, id: string, updates: Partial<T>): T | null {
    const items = (this as any)[collection]
    if (!items) throw new Error(`Collection ${collection} not found`)

    const index = items.findIndex((item: any) => item.id === id)
    if (index === -1) return null

    items[index] = { ...items[index], ...updates, updatedAt: new Date().toISOString() }
    return items[index]
  }

  delete(collection: string, id: string): boolean {
    const items = (this as any)[collection]
    if (!items) throw new Error(`Collection ${collection} not found`)

    const index = items.findIndex((item: any) => item.id === id)
    if (index === -1) return false

    items.splice(index, 1)
    return true
  }

  // Search and filter
  search<T>(collection: string, query: string, fields: string[]): T[] {
    const items = this.getAll<T>(collection)
    if (!query) return items

    return items.filter((item: any) =>
      fields.some((field) => item[field]?.toString().toLowerCase().includes(query.toLowerCase())),
    )
  }

  filter<T>(collection: string, filters: Record<string, any>): T[] {
    const items = this.getAll<T>(collection)

    return items.filter((item: any) =>
      Object.entries(filters).every(([key, value]) => {
        if (value === undefined || value === null || value === "") return true
        return item[key] === value
      }),
    )
  }

  // Pagination
  paginate<T>(items: T[], page = 1, limit = 10) {
    const offset = (page - 1) * limit
    const paginatedItems = items.slice(offset, offset + limit)

    return {
      data: paginatedItems,
      pagination: {
        page,
        limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
      },
    }
  }

  // Generate ID
  generateId(prefix: string): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 5)
    return `${prefix}-${timestamp}${random}`.toUpperCase()
  }
}

export const db = new InMemoryDatabase()
