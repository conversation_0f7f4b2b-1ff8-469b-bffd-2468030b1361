"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card.jsx"
import { Button } from "../../components/ui/button.jsx"
import { Input } from "../../components/ui/input.jsx"
import { Label } from "../../components/ui/label.jsx"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select.jsx"
import { Badge } from "../../components/ui/badge.jsx"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs.jsx"
import { Search, Database, Download, RefreshCw } from "lucide-react"

const searchResults = [
  {
    table: "customers",
    record: {
      id: "CUST-001",
      name: "Acme Corporation",
      email: "<EMAIL>",
      city: "Mumbai",
      type: "Enterprise",
      status: "Active",
    },
    relevance: 95,
  },
  {
    table: "transactions",
    record: {
      id: "TXN-001",
      customer_id: "CUST-001",
      amount: 15000,
      status: "Completed",
      date: "2024-01-15",
    },
    relevance: 88,
  },
  {
    table: "customer_journey",
    record: {
      id: "CJ-001",
      customer_id: "CUST-001",
      stage: "Conversion",
      touchpoint: "Website",
      date: "2024-01-10",
    },
    relevance: 82,
  },
]

const savedQueries = [
  {
    id: 1,
    name: "Active Enterprise Customers",
    query: "SELECT * FROM customers WHERE type = 'Enterprise' AND status = 'Active'",
    lastRun: "2024-01-25",
    results: 45,
  },
  {
    id: 2,
    name: "High Value Transactions",
    query: "SELECT * FROM transactions WHERE amount > 10000",
    lastRun: "2024-01-24",
    results: 128,
  },
  {
    id: 3,
    name: "Recent Customer Journeys",
    query: "SELECT * FROM customer_journey WHERE date >= '2024-01-01'",
    lastRun: "2024-01-23",
    results: 234,
  },
]

const tableSchemas = [
  {
    table: "customers",
    columns: ["id", "name", "email", "phone", "city", "type", "status", "created_at"],
    records: 15234,
  },
  {
    table: "transactions",
    columns: ["id", "customer_id", "amount", "type", "status", "method", "date"],
    records: 89567,
  },
  {
    table: "customer_journey",
    columns: ["id", "customer_id", "stage", "touchpoint", "source", "date"],
    records: 45123,
  },
  {
    table: "analytics_cache",
    columns: ["id", "metric", "value", "date", "filters"],
    records: 12890,
  },
]

export default function DatabaseSearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTable, setSelectedTable] = useState("all")
  const [searchType, setSearchType] = useState("text")
  const [isSearching, setIsSearching] = useState(false)
  const [results, setResults] = useState([])

  const handleSearch = () => {
    setIsSearching(true)
    // Simulate search
    setTimeout(() => {
      setResults(searchResults)
      setIsSearching(false)
    }, 1500)
  }

  const handleRunSavedQuery = (query) => {
    console.log("Running query:", query)
    // In a real app, this would execute the SQL query
  }

  const getRelevanceColor = (relevance) => {
    if (relevance >= 90) return "bg-green-100 text-green-800"
    if (relevance >= 70) return "bg-yellow-100 text-yellow-800"
    return "bg-red-100 text-red-800"
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Database Search</h1>
          <p className="text-muted-foreground">Search across all database tables and records</p>
        </div>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Results
        </Button>
      </div>

      <Tabs defaultValue="search" className="space-y-6">
        <TabsList>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="saved">Saved Queries</TabsTrigger>
          <TabsTrigger value="schema">Database Schema</TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          {/* Search Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>Database Search</span>
              </CardTitle>
              <CardDescription>Search for records across all database tables</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="search-query">Search Query</Label>
                  <Input
                    id="search-query"
                    placeholder="Enter search terms..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="table-select">Table</Label>
                  <Select value={selectedTable} onValueChange={setSelectedTable}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select table" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Tables</SelectItem>
                      <SelectItem value="customers">Customers</SelectItem>
                      <SelectItem value="transactions">Transactions</SelectItem>
                      <SelectItem value="customer_journey">Customer Journey</SelectItem>
                      <SelectItem value="analytics_cache">Analytics Cache</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="search-type">Search Type</Label>
                  <Select value={searchType} onValueChange={setSearchType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select search type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">Text Search</SelectItem>
                      <SelectItem value="exact">Exact Match</SelectItem>
                      <SelectItem value="fuzzy">Fuzzy Search</SelectItem>
                      <SelectItem value="regex">Regular Expression</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button onClick={handleSearch} disabled={isSearching || !searchQuery} className="w-full">
                {isSearching ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Search Database
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Search Results */}
          {results.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Search Results</CardTitle>
                <CardDescription>Found {results.length} matching records</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {results.map((result, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Database className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{result.table}</span>
                        </div>
                        <Badge className={getRelevanceColor(result.relevance)}>{result.relevance}% match</Badge>
                      </div>
                      <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                        {Object.entries(result.record).map(([key, value]) => (
                          <div key={key} className="text-sm">
                            <span className="text-muted-foreground">{key}:</span>
                            <span className="ml-2 font-medium">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="saved" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Saved Queries</CardTitle>
              <CardDescription>Frequently used database queries</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {savedQueries.map((query) => (
                  <div key={query.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-2">
                      <div className="font-medium">{query.name}</div>
                      <div className="text-sm text-muted-foreground font-mono bg-muted p-2 rounded">{query.query}</div>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <span>Last run: {query.lastRun}</span>
                        <span>Results: {query.results}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleRunSavedQuery(query.query)}>
                        Run Query
                      </Button>
                      <Button size="sm" variant="outline">
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schema" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Database Schema</span>
              </CardTitle>
              <CardDescription>Overview of database tables and their structure</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {tableSchemas.map((schema, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-medium text-lg">{schema.table}</h3>
                      <Badge variant="secondary">{schema.records.toLocaleString()} records</Badge>
                    </div>
                    <div className="grid gap-2 md:grid-cols-4 lg:grid-cols-6">
                      {schema.columns.map((column, colIndex) => (
                        <Badge key={colIndex} variant="outline" className="justify-center">
                          {column}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
