"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "./ui/button.jsx"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card.jsx"
import { Textarea } from "./ui/textarea.jsx"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "./ui/tabs.jsx"

// Changed from AiInsights to AIInsights to match imports
export function AIInsights() {
  const [query, setQuery] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState(null)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!query.trim()) return

    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      // This would be replaced with actual AI processing
      const sampleResponses = [
        "Based on the data, your top-performing event was the Digital Signage Expo with 1,200 attendees and $45,000 in revenue. This represents a 15% increase from the previous year.",
        "Your customer acquisition rate has increased by 18% in the last quarter, with the highest growth in the enterprise segment.",
        "The data shows that companies in the retail sector are most likely to advertise in your magazine, with an average spend of $12,500 per campaign.",
        "Looking at demographic trends, your audience is primarily composed of marketing professionals (45%) and business owners (30%) in the 35-55 age range.",
        "Revenue from digital advertising has grown 28% year-over-year, outpacing print advertising which grew at 5%.",
      ]

      setResult(sampleResponses[Math.floor(Math.random() * sampleResponses.length)])
      setIsLoading(false)
    }, 1500)
  }

  const sampleQueries = [
    "Which event generated the most revenue last year?",
    "What is our customer acquisition trend?",
    "Who are our top advertising clients?",
    "What demographics engage most with our content?",
    "Compare digital vs print advertising revenue",
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-purple-500" />
          AI Insights
        </CardTitle>
        <CardDescription>Ask questions about your data in natural language</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="query">
          <TabsList className="mb-4">
            <TabsTrigger value="query">Ask a Question</TabsTrigger>
            <TabsTrigger value="suggestions">Suggested Queries</TabsTrigger>
          </TabsList>

          <TabsContent value="query">
            <form onSubmit={handleSubmit} className="space-y-4">
              <Textarea
                placeholder="Ask about your data, e.g., 'What was our best performing event last quarter?'"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="min-h-[100px]"
              />
              <Button type="submit" className="w-full" disabled={isLoading || !query.trim()}>
                {isLoading ? (
                  <>Analyzing...</>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Get Insights
                  </>
                )}
              </Button>
            </form>

            {result && (
              <div className="mt-4 rounded-lg border p-4">
                <div className="flex items-start gap-3">
                  <Bot className="mt-1 h-5 w-5 text-purple-500" />
                  <div className="flex-1">
                    <p className="text-sm">{result}</p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="suggestions">
            <div className="grid gap-2">
              {sampleQueries.map((q, i) => (
                <Button
                  key={i}
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    setQuery(q)
                    document.querySelector('[value="query"]')?.dispatchEvent(new MouseEvent("click", { bubbles: true }))
                  }}
                >
                  <Sparkles className="mr-2 h-4 w-4 text-purple-500" />
                  {q}
                </Button>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
