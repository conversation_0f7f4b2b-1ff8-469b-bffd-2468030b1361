// Form validation schemas and utilities
export const validateEmail = (email) => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i
  return emailRegex.test(email)
}

export const validatePhone = (phone) => {
  const phoneRegex = /^[+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/\s/g, ""))
}

export const validateRequired = (value) => {
  return value && value.toString().trim().length > 0
}

export const validateMinLength = (value, minLength) => {
  return value && value.toString().length >= minLength
}

export const validateMaxLength = (value, maxLength) => {
  return value && value.toString().length <= maxLength
}

export const validatePassword = (password) => {
  // At least 6 characters
  return password && password.length >= 6
}

export const validateUrl = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Customer validation
export const validateCustomer = (customer) => {
  const errors = {}

  if (!validateRequired(customer.name)) {
    errors.name = "Company name is required"
  }

  if (!validateRequired(customer.email)) {
    errors.email = "Email is required"
  } else if (!validateEmail(customer.email)) {
    errors.email = "Invalid email address"
  }

  if (!validateRequired(customer.phone)) {
    errors.phone = "Phone number is required"
  } else if (!validatePhone(customer.phone)) {
    errors.phone = "Invalid phone number"
  }

  if (!validateRequired(customer.address)) {
    errors.address = "Address is required"
  }

  if (!validateRequired(customer.city)) {
    errors.city = "City is required"
  }

  if (!validateRequired(customer.state)) {
    errors.state = "State is required"
  }

  if (!validateRequired(customer.country)) {
    errors.country = "Country is required"
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Transaction validation
export const validateTransaction = (transaction) => {
  const errors = {}

  if (!validateRequired(transaction.amount)) {
    errors.amount = "Amount is required"
  } else if (isNaN(transaction.amount) || Number(transaction.amount) <= 0) {
    errors.amount = "Amount must be a positive number"
  }

  if (!validateRequired(transaction.date)) {
    errors.date = "Date is required"
  }

  if (!validateRequired(transaction.description)) {
    errors.description = "Description is required"
  }

  if (!validateRequired(transaction.medium)) {
    errors.medium = "Medium is required"
  }

  if (!validateRequired(transaction.channel)) {
    errors.channel = "Channel is required"
  }

  if (!validateRequired(transaction.property)) {
    errors.property = "Property is required"
  }

  if (!validateRequired(transaction.variant)) {
    errors.variant = "Variant is required"
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}
