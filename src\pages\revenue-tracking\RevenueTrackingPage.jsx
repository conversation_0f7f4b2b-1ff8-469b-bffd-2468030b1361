"use client"

import { useState, useMemo } from "react"
import {
  Download,
  Search,
  Filter,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  FileSpreadsheet,
  BarChart3,
} from "lucide-react"

import { <PERSON><PERSON> } from "../../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import { Input } from "../../components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu"
import { Label } from "../../components/ui/label"
import { formatCurrency } from "../../lib/utils"
import { <PERSON><PERSON><PERSON> } from "../../components/charts/bar-chart"
import { <PERSON><PERSON><PERSON> } from "../../components/charts/line-chart"
import { Pie<PERSON><PERSON> } from "../../components/charts/pie-chart"
import { AreaChart } from "../../components/charts/area-chart"
import { ChartCard } from "../../components/charts/chart-card"
import { DrillDownDialog } from "../../components/charts/drill-down-dialog"
import { MobileHeader } from "../../components/layout/mobile-header"
import { Popover, PopoverContent, PopoverTrigger } from "../../components/ui/popover"

// Sample data based on the provided schema
const revenueData = [
  {
    id: 1,
    companyName: "Xtreme Media",
    city: "Mumbai",
    contactStatus: "Solution Provider",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Cover Page",
    month: "Jan",
    year: 2024,
    revenue: 110000,
  },
  {
    id: 2,
    companyName: "Singpost",
    city: "Bangalore",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Full Page",
    month: "Jan",
    year: 2024,
    revenue: 75000,
  },
  {
    id: 3,
    companyName: "Digital Signage Co",
    city: "Delhi",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Website",
    property: "Advt",
    variant: "Banner",
    month: "Jan",
    year: 2024,
    revenue: 45000,
  },
  {
    id: 4,
    companyName: "OOH Media Group",
    city: "Mumbai",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Half Page",
    month: "Feb",
    year: 2024,
    revenue: 50000,
  },
  {
    id: 5,
    companyName: "Xtreme Media",
    city: "Mumbai",
    contactStatus: "Solution Provider",
    medium: "Event",
    channel: "OAC Asia",
    property: "Sponsorship",
    variant: "Gold",
    month: "Feb",
    year: 2024,
    revenue: 150000,
  },
  {
    id: 6,
    companyName: "Display Solutions",
    city: "Chennai",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Newsletter",
    property: "Advt",
    variant: "Featured",
    month: "Feb",
    year: 2024,
    revenue: 35000,
  },
  {
    id: 7,
    companyName: "Signage Tech",
    city: "Hyderabad",
    contactStatus: "Solution Provider",
    medium: "Event",
    channel: "OAC Asia",
    property: "Sponsorship",
    variant: "Silver",
    month: "Mar",
    year: 2024,
    revenue: 100000,
  },
  {
    id: 8,
    companyName: "Media Innovations",
    city: "Pune",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Full Page",
    month: "Mar",
    year: 2024,
    revenue: 75000,
  },
  {
    id: 9,
    companyName: "Digital Displays Inc",
    city: "Bangalore",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Website",
    property: "Advt",
    variant: "Sidebar",
    month: "Mar",
    year: 2024,
    revenue: 30000,
  },
  {
    id: 10,
    companyName: "Outdoor Solutions",
    city: "Delhi",
    contactStatus: "Client",
    medium: "Event",
    channel: "OAC Asia",
    property: "Booth",
    variant: "Standard",
    month: "Apr",
    year: 2024,
    revenue: 85000,
  },
  {
    id: 11,
    companyName: "Singpost",
    city: "Bangalore",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Full Page",
    month: "Apr",
    year: 2024,
    revenue: 75000,
  },
  {
    id: 12,
    companyName: "Xtreme Media",
    city: "Mumbai",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Newsletter",
    property: "Advt",
    variant: "Featured",
    month: "Apr",
    year: 2024,
    revenue: 40000,
  },
  // Previous year data for comparison
  {
    id: 13,
    companyName: "Xtreme Media",
    city: "Mumbai",
    contactStatus: "Solution Provider",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Cover Page",
    month: "Jan",
    year: 2023,
    revenue: 100000,
  },
  {
    id: 14,
    companyName: "Singpost",
    city: "Bangalore",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Full Page",
    month: "Jan",
    year: 2023,
    revenue: 70000,
  },
  {
    id: 15,
    companyName: "Digital Signage Co",
    city: "Delhi",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Website",
    property: "Advt",
    variant: "Banner",
    month: "Jan",
    year: 2023,
    revenue: 40000,
  },
  {
    id: 16,
    companyName: "OOH Media Group",
    city: "Mumbai",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Half Page",
    month: "Feb",
    year: 2023,
    revenue: 45000,
  },
  {
    id: 17,
    companyName: "Xtreme Media",
    city: "Mumbai",
    contactStatus: "Solution Provider",
    medium: "Event",
    channel: "OAC Asia",
    property: "Sponsorship",
    variant: "Gold",
    month: "Feb",
    year: 2023,
    revenue: 140000,
  },
  {
    id: 18,
    companyName: "Display Solutions",
    city: "Chennai",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Newsletter",
    property: "Advt",
    variant: "Featured",
    month: "Feb",
    year: 2023,
    revenue: 30000,
  },
  {
    id: 19,
    companyName: "Signage Tech",
    city: "Hyderabad",
    contactStatus: "Solution Provider",
    medium: "Event",
    channel: "OAC Asia",
    property: "Sponsorship",
    variant: "Silver",
    month: "Mar",
    year: 2023,
    revenue: 90000,
  },
  {
    id: 20,
    companyName: "Media Innovations",
    city: "Pune",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Full Page",
    month: "Mar",
    year: 2023,
    revenue: 70000,
  },
  {
    id: 21,
    companyName: "Digital Displays Inc",
    city: "Bangalore",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Website",
    property: "Advt",
    variant: "Sidebar",
    month: "Mar",
    year: 2023,
    revenue: 25000,
  },
  {
    id: 22,
    companyName: "Outdoor Solutions",
    city: "Delhi",
    contactStatus: "Client",
    medium: "Event",
    channel: "OAC Asia",
    property: "Booth",
    variant: "Standard",
    month: "Apr",
    year: 2023,
    revenue: 80000,
  },
  {
    id: 23,
    companyName: "Singpost",
    city: "Bangalore",
    contactStatus: "Client",
    medium: "Print",
    channel: "OA Mag",
    property: "Advt",
    variant: "Full Page",
    month: "Apr",
    year: 2023,
    revenue: 70000,
  },
  {
    id: 24,
    companyName: "Xtreme Media",
    city: "Mumbai",
    contactStatus: "Solution Provider",
    medium: "Digital",
    channel: "Newsletter",
    property: "Advt",
    variant: "Featured",
    month: "Apr",
    year: 2023,
    revenue: 35000,
  },
]

// Get unique values for filters
const getUniqueValues = (data, key) => {
  if (!data || !Array.isArray(data) || data.length === 0) return []
  return [
    ...new Set(data.filter((item) => item && item[key] !== undefined && item[key] !== null).map((item) => item[key])),
  ].sort()
}

export default function RevenueTrackingPage() {
  // Filter states
  const [selectedYear, setSelectedYear] = useState<string>("2024")
  const [selectedCompany, setSelectedCompany] = useState<string>("all")
  const [selectedCity, setSelectedCity] = useState<string>("all")
  const [selectedMedium, setSelectedMedium] = useState<string>("all")
  const [selectedChannel, setSelectedChannel] = useState<string>("all")
  const [selectedProperty, setSelectedProperty] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [groupBy, setGroupBy] = useState<string>("medium")
  const [timeFrame, setTimeFrame] = useState<string>("monthly")
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false)

  // Drill down state
  const [drillDownOpen, setDrillDownOpen] = useState(false)
  const [drillDownData, setDrillDownData] = useState<any>(null)
  const [drillDownType, setDrillDownType] = useState<
    "medium" | "channel" | "company" | "city" | "property" | "variant"
  >("medium")
  const [drillDownTitle, setDrillDownTitle] = useState("")

  // Get unique values for filters
  const companies = getUniqueValues(revenueData, "companyName")
  const cities = getUniqueValues(revenueData, "city")
  const mediums = getUniqueValues(revenueData, "medium")
  const channels = getUniqueValues(revenueData, "channel")
  const properties = getUniqueValues(revenueData, "property")
  const years = getUniqueValues(revenueData, "year")

  // Filter data based on selected filters
  const filteredData = useMemo(() => {
    if (!revenueData || !Array.isArray(revenueData)) return []

    return revenueData.filter((item) => {
      if (!item) return false

      const matchesYear = selectedYear === "all" || (item.year && item.year.toString() === selectedYear)
      const matchesCompany = selectedCompany === "all" || (item.companyName && item.companyName === selectedCompany)
      const matchesCity = selectedCity === "all" || (item.city && item.city === selectedCity)
      const matchesMedium = selectedMedium === "all" || (item.medium && item.medium === selectedMedium)
      const matchesChannel = selectedChannel === "all" || (item.channel && item.channel === selectedChannel)
      const matchesProperty = selectedProperty === "all" || (item.property && item.property === selectedProperty)

      const matchesSearch =
        searchQuery === "" ||
        (item.companyName && item.companyName.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.city && item.city.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.medium && item.medium.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.channel && item.channel.toLowerCase().includes(searchQuery.toLowerCase()))

      return (
        matchesYear &&
        matchesCompany &&
        matchesCity &&
        matchesMedium &&
        matchesChannel &&
        matchesProperty &&
        matchesSearch
      )
    })
  }, [selectedYear, selectedCompany, selectedCity, selectedMedium, selectedChannel, selectedProperty, searchQuery])

  // Calculate total revenue
  const totalRevenue = useMemo(() => {
    if (!filteredData || !Array.isArray(filteredData)) return 0
    return filteredData.reduce((sum, item) => sum + (item?.revenue || 0), 0)
  }, [filteredData])

  // Calculate previous year's revenue for comparison
  const previousYearRevenue = useMemo(() => {
    if (!revenueData || !Array.isArray(revenueData) || !selectedYear) return 0
    const prevYear = Number.parseInt(selectedYear) - 1
    return revenueData
      .filter((item) => item && item.year === prevYear)
      .reduce((sum, item) => sum + (item?.revenue || 0), 0)
  }, [selectedYear])

  // Calculate percentage change
  const percentageChange = useMemo(() => {
    if (previousYearRevenue === 0) return 0
    return ((totalRevenue - previousYearRevenue) / previousYearRevenue) * 100
  }, [totalRevenue, previousYearRevenue])

  // Group data for charts based on selected groupBy
  const groupedChartData = useMemo(() => {
    if (!filteredData || !Array.isArray(filteredData) || !groupBy) return []

    const grouped =  {}

    filteredData.forEach((item) => {
      if (!item) return

      const key = item[item]
      if (!key) return

      if (!grouped[key]) {
        grouped[key] = 0
      }
      grouped[key] += item.revenue || 0
    })

    return Object.keys(grouped).map((key) => ({
      name: key,
      value: grouped[key],
    }))
  }, [filteredData, groupBy])

  // Prepare time series data
  const timeSeriesData = useMemo(() => {
    if (!revenueData || !Array.isArray(revenueData) || !selectedYear) return []

    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    const currentYearData = {}
    const previousYearData = {}

    // Initialize with zero values
    months.forEach((month) => {
      currentYearData[month] = 0
      previousYearData[month] = 0
    })

    // Fill in actual data
    revenueData.forEach((item) => {
      if (!item || !item.month) return

      if (item.year && item.year.toString() === selectedYear) {
        currentYearData[item.month] = (currentYearData[item.month] || 0) + (item.revenue || 0)
      } else if (item.year === Number.parseInt(selectedYear) - 1) {
        previousYearData[item.month] = (previousYearData[item.month] || 0) + (item.revenue || 0)
      }
    })

    // Convert to array format for chart
    return months.map((month) => ({
      month,
      current: currentYearData[month] || 0,
      previous: previousYearData[month] || 0,
    }))
  }, [selectedYear])

  // Top companies by revenue
  const topCompanies = useMemo(() => {
    if (!filteredData || !Array.isArray(filteredData)) return []

    const companyRevenue = {}

    filteredData.forEach((item) => {
      if (!item || !item.companyName) return

      if (!companyRevenue[item.companyName]) {
        companyRevenue[item.companyName] = 0
      }
      companyRevenue[item.companyName] += item.revenue || 0
    })

    return Object.entries(companyRevenue)
      .map(([name, revenue]) => ({ name, revenue }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)
  }, [filteredData])

  // Handle drill down
  const handleDrillDown = (data, type) => {
    if (!data || !data.name) return

    setDrillDownData(data)
    setDrillDownType(type)
    setDrillDownTitle(`${type.charAt(0).toUpperCase() + type.slice(1)}: ${data.name}`)
    setDrillDownOpen(true)
  }

  return (
    <div className="flex flex-col min-h-screen">
      <MobileHeader />
      <div className="border-b hidden md:block">
        <div className="flex h-16 items-center px-4 gap-4">
          <h1 className="text-lg font-semibold">Revenue Tracking</h1>
          <div className="ml-auto flex items-center gap-4 flex-wrap">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search companies, cities..."
                className="w-[200px] lg:w-[300px] pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Years</SelectItem>
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-4" align="end">
                <div className="space-y-4">
                  <h4 className="font-medium">Filter Options</h4>

                  <div className="space-y-2">
                    <Label htmlFor="company-filter">Company</Label>
                    <Select value={selectedCompany} onValueChange={setSelectedCompany}>
                      <SelectTrigger id="company-filter">
                        <SelectValue placeholder="Select company" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Companies</SelectItem>
                        {companies.map((company) => (
                          <SelectItem key={company} value={company}>
                            {company}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city-filter">City</Label>
                    <Select value={selectedCity} onValueChange={setSelectedCity}>
                      <SelectTrigger id="city-filter">
                        <SelectValue placeholder="Select city" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Cities</SelectItem>
                        {cities.map((city) => (
                          <SelectItem key={city} value={city}>
                            {city}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="medium-filter">Medium</Label>
                    <Select value={selectedMedium} onValueChange={setSelectedMedium}>
                      <SelectTrigger id="medium-filter">
                        <SelectValue placeholder="Select medium" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Mediums</SelectItem>
                        {mediums.map((medium) => (
                          <SelectItem key={medium} value={medium}>
                            {medium}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="channel-filter">Channel</Label>
                    <Select value={selectedChannel} onValueChange={setSelectedChannel}>
                      <SelectTrigger id="channel-filter">
                        <SelectValue placeholder="Select channel" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Channels</SelectItem>
                        {channels.map((channel) => (
                          <SelectItem key={channel} value={channel}>
                            {channel}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="property-filter">Property</Label>
                    <Select value={selectedProperty} onValueChange={setSelectedProperty}>
                      <SelectTrigger id="property-filter">
                        <SelectValue placeholder="Select property" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Properties</SelectItem>
                        {properties.map((property) => (
                          <SelectItem key={property} value={property}>
                            {property}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-between pt-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedCompany("all")
                        setSelectedCity("all")
                        setSelectedMedium("all")
                        setSelectedChannel("all")
                        setSelectedProperty("all")
                        setSearchQuery("")
                      }}
                    >
                      Reset
                    </Button>
                    <Button onClick={() => setIsFilterOpen(false)}>Apply Filters</Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Select value={groupBy} onValueChange={setGroupBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Group by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="channel">Channel</SelectItem>
                <SelectItem value="property">Property</SelectItem>
                <SelectItem value="variant">Variant</SelectItem>
                <SelectItem value="city">City</SelectItem>
                <SelectItem value="companyName">Company</SelectItem>
              </SelectContent>
            </Select>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <FileSpreadsheet className="mr-2 h-4 w-4" />
                  Export as Excel
                </DropdownMenuItem>
                <DropdownMenuItem>Export as PDF</DropdownMenuItem>
                <DropdownMenuItem>Export as CSV</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Schedule Reports</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        {/* Mobile controls */}
        <div className="md:hidden mb-6">
          <h1 className="text-2xl font-bold mb-4">Revenue Tracking</h1>
          <div className="flex flex-col gap-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search companies, cities..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger>
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Years</SelectItem>
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" className="w-full flex justify-between" onClick={() => setIsFilterOpen(true)}>
              <span>Filters</span>
              <Filter className="h-4 w-4" />
            </Button>
            <Select value={groupBy} onValueChange={setGroupBy}>
              <SelectTrigger>
                <SelectValue placeholder="Group by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="channel">Channel</SelectItem>
                <SelectItem value="property">Property</SelectItem>
                <SelectItem value="variant">Variant</SelectItem>
                <SelectItem value="city">City</SelectItem>
                <SelectItem value="companyName">Company</SelectItem>
              </SelectContent>
            </Select>
            <Button className="w-full">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Revenue Overview Cards */}
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue ({selectedYear})</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {percentageChange >= 0 ? (
                  <>
                    <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                    <span className="text-emerald-500 font-medium">+{percentageChange.toFixed(1)}%</span>
                  </>
                ) : (
                  <>
                    <ArrowDownRight className="mr-1 h-4 w-4 text-rose-500" />
                    <span className="text-rose-500 font-medium">{percentageChange.toFixed(1)}%</span>
                  </>
                )}
                <span className="ml-1">vs. previous year</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredData.length}</div>
              <div className="text-xs text-muted-foreground">
                Avg. Value: {formatCurrency(totalRevenue / (filteredData.length || 1))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Medium</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {groupedChartData.length > 0 ? (
                <>
                  <div className="text-2xl font-bold">
                    {groupedChartData.sort((a, b) => b.value - a.value)[0]?.name || "N/A"}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatCurrency(groupedChartData.sort((a, b) => b.value - a.value)[0]?.value || 0)}
                  </div>
                </>
              ) : (
                <>
                  <div className="text-2xl font-bold">N/A</div>
                  <div className="text-xs text-muted-foreground">No data available</div>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Company</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {topCompanies.length > 0 ? (
                <>
                  <div className="text-2xl font-bold">{topCompanies[0]?.name || "N/A"}</div>
                  <div className="text-xs text-muted-foreground">{formatCurrency(topCompanies[0]?.revenue || 0)}</div>
                </>
              ) : (
                <>
                  <div className="text-2xl font-bold">N/A</div>
                  <div className="text-xs text-muted-foreground">No data available</div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="overflow-x-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <ChartCard
                title={`Revenue by ${groupBy.charAt(0).toUpperCase() + groupBy.slice(1)}`}
                description={`Distribution of revenue across different ${groupBy} categories`}
                onDrillDown={() => {
                  const topItem = groupedChartData.sort((a, b) => b.value - a.value)[0]
                  if (topItem) {
                    handleDrillDown(
                      topItem,
                      groupBy,
                    )
                  }
                }}
              >
                <PieChart data={groupedChartData} valueFormatter={(value) => formatCurrency(value)} />
              </ChartCard>

              <ChartCard title="Monthly Revenue Comparison" description="Current year vs previous year">
                <BarChart
                  data={timeSeriesData}
                  categories={{
                    current: {
                      label: `${selectedYear} Revenue`,
                      color: "hsl(var(--chart-1))",
                    },
                    previous: {
                      label: `${Number.parseInt(selectedYear) - 1} Revenue`,
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  index="month"
                  valueFormatter={(value) => formatCurrency(value)}
                />
              </ChartCard>
            </div>

            <ChartCard title="Top Companies by Revenue" description="Highest revenue generating companies">
              <BarChart
                data={topCompanies}
                categories={{
                  revenue: {
                    label: "Revenue",
                    color: "hsl(var(--chart-3))",
                  },
                }}
                index="name"
                layout="vertical"
                valueFormatter={(value) => formatCurrency(value)}
                onDrillDown={() => {
                  const topCompany = topCompanies[0]
                  if (topCompany) {
                    handleDrillDown(topCompany, "company")
                  }
                }}
              />
            </ChartCard>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <ChartCard title="Revenue Trends Over Time" description="Monthly revenue trends for selected period">
              <LineChart
                data={timeSeriesData}
                categories={{
                  current: {
                    label: `${selectedYear} Revenue`,
                    color: "hsl(var(--chart-1))",
                  },
                  previous: {
                    label: `${Number.parseInt(selectedYear) - 1} Revenue`,
                    color: "hsl(var(--chart-2))",
                  },
                }}
                index="month"
                valueFormatter={(value) => formatCurrency(value)}
              />
            </ChartCard>

            <div className="grid gap-4 md:grid-cols-2">
              <ChartCard
                title="Medium Performance"
                description="Revenue by medium over time"
                onDrillDown={() => {
                  const mediumData = { name: "Print", value: 185000 }
                  handleDrillDown(mediumData, "medium")
                }}
              >
                <LineChart
                  data={[
                    { month: "Jan", Print: 185000, Digital: 45000, Event: 0 },
                    { month: "Feb", Print: 50000, Digital: 35000, Event: 150000 },
                    { month: "Mar", Print: 75000, Digital: 30000, Event: 100000 },
                    { month: "Apr", Print: 75000, Digital: 40000, Event: 85000 },
                  ]}
                  categories={{
                    Print: {
                      label: "Print",
                      color: "hsl(var(--chart-1))",
                    },
                    Digital: {
                      label: "Digital",
                      color: "hsl(var(--chart-2))",
                    },
                    Event: {
                      label: "Event",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  index="month"
                  valueFormatter={(value) => formatCurrency(value)}
                />
              </ChartCard>

              <ChartCard
                title="Channel Performance"
                description="Revenue by channel over time"
                onDrillDown={() => {
                  const channelData = { name: "OA Mag", value: 185000 }
                  handleDrillDown(channelData, "channel")
                }}
              >
                <AreaChart
                  data={[
                    { month: "Jan", "OA Mag": 185000, Website: 45000, Newsletter: 0, "OAC Asia": 0 },
                    { month: "Feb", "OA Mag": 50000, Website: 0, Newsletter: 35000, "OAC Asia": 150000 },
                    { month: "Mar", "OA Mag": 75000, Website: 30000, Newsletter: 0, "OAC Asia": 100000 },
                    { month: "Apr", "OA Mag": 75000, Website: 0, Newsletter: 40000, "OAC Asia": 85000 },
                  ]}
                  categories={{
                    "OA Mag": {
                      label: "OA Mag",
                      color: "hsl(var(--chart-1))",
                    },
                    Website: {
                      label: "Website",
                      color: "hsl(var(--chart-2))",
                    },
                    Newsletter: {
                      label: "Newsletter",
                      color: "hsl(var(--chart-3))",
                    },
                    "OAC Asia": {
                      label: "OAC Asia",
                      color: "hsl(var(--chart-4))",
                    },
                  }}
                  index="month"
                  valueFormatter={(value) => formatCurrency(value)}
                  stack={true}
                />
              </ChartCard>
            </div>
          </TabsContent>

          <TabsContent value="breakdown" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <ChartCard
                title="Revenue by Medium"
                description="Distribution across different mediums"
                onDrillDown={() => {
                  const mediumData = revenueData
                    .reduce(
                      (acc, item) => {
                        if (!item || !item.medium) return acc

                        const medium = item.medium
                        const existingMedium = acc.find((m) => m.name === medium)
                        if (existingMedium) {
                          existingMedium.value += item.revenue || 0
                        } else {
                          acc.push({ name: medium, value: item.revenue || 0 })
                        }
                        return acc
                      },
                      []
                    )
                    .sort((a, b) => b.value - a.value)[0]

                  if (mediumData) {
                    handleDrillDown(mediumData, "medium")
                  }
                }}
              >
                <PieChart
                  data={revenueData
                    .reduce(
                      (acc, item) => {
                        if (!item || !item.medium) return acc

                        const medium = item.medium
                        const existingMedium = acc.find((m) => m.name === medium)
                        if (existingMedium) {
                          existingMedium.value += item.revenue || 0
                        } else {
                          acc.push({ name: medium, value: item.revenue || 0 })
                        }
                        return acc
                      },
                      []
                    )
                    .sort((a, b) => b.value - a.value)}
                  valueFormatter={(value) => formatCurrency(value)}
                />
              </ChartCard>

              <ChartCard
                title="Revenue by Channel"
                description="Distribution across different channels"
                onDrillDown={() => {
                  const channelData = revenueData
                    .reduce(
                      (acc, item) => {
                        if (!item || !item.channel) return acc

                        const channel = item.channel
                        const existingChannel = acc.find((c) => c.name === channel)
                        if (existingChannel) {
                          existingChannel.value += item.revenue || 0
                        } else {
                          acc.push({ name: channel, value: item.revenue || 0 })
                        }
                        return acc
                      },
                      []
                    )
                    .sort((a, b) => b.value - a.value)[0]

                  if (channelData) {
                    handleDrillDown(channelData, "channel")
                  }
                }}
              >
                <PieChart
                  data={revenueData
                    .reduce(
                      (acc, item) => {
                        if (!item || !item.channel) return acc

                        const channel = item.channel
                        const existingChannel = acc.find((c) => c.name === channel)
                        if (existingChannel) {
                          existingChannel.value += item.revenue || 0
                        } else {
                          acc.push({ name: channel, value: item.revenue || 0 })
                        }
                        return acc
                      },
                      []
                    )
                    .sort((a, b) => b.value - a.value)}
                  valueFormatter={(value) => formatCurrency(value)}
                />
              </ChartCard>

              <ChartCard
                title="Revenue by City"
                description="Distribution across different cities"
                onDrillDown={() => {
                  const cityData = revenueData
                    .reduce(
                      (acc, item) => {
                        if (!item || !item.city) return acc

                        const city = item.city
                        const existingCity = acc.find((c) => c.name === city)
                        if (existingCity) {
                          existingCity.value += item.revenue || 0
                        } else {
                          acc.push({ name: city, value: item.revenue || 0 })
                        }
                        return acc
                      },
                      []
                    )
                    .sort((a, b) => b.value - a.value)[0]

                  if (cityData) {
                    handleDrillDown(cityData, "city")
                  }
                }}
              >
                <PieChart
                  data={revenueData
                    .reduce(
                      (acc, item) => {
                        if (!item || !item.city) return acc

                        const city = item.city
                        const existingCity = acc.find((c) => c.name === city)
                        if (existingCity) {
                          existingCity.value += item.revenue || 0
                        } else {
                          acc.push({ name: city, value: item.revenue || 0 })
                        }
                        return acc
                      },
                      []
                    )
                    .sort((a, b) => b.value - a.value)}
                  valueFormatter={(value) => formatCurrency(value)}
                />
              </ChartCard>
            </div>

            <ChartCard title="Revenue by Company Type" description="Client vs Solution Provider">
              <BarChart
                data={[
                  {
                    month: "Jan",
                    Client: revenueData
                      .filter((item) => item && item.month === "Jan" && item.contactStatus === "Client")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                    "Solution Provider": revenueData
                      .filter((item) => item && item.month === "Jan" && item.contactStatus === "Solution Provider")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                  },
                  {
                    month: "Feb",
                    Client: revenueData
                      .filter((item) => item && item.month === "Feb" && item.contactStatus === "Client")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                    "Solution Provider": revenueData
                      .filter((item) => item && item.month === "Feb" && item.contactStatus === "Solution Provider")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                  },
                  {
                    month: "Mar",
                    Client: revenueData
                      .filter((item) => item && item.month === "Mar" && item.contactStatus === "Client")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                    "Solution Provider": revenueData
                      .filter((item) => item && item.month === "Mar" && item.contactStatus === "Solution Provider")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                  },
                  {
                    month: "Apr",
                    Client: revenueData
                      .filter((item) => item && item.month === "Apr" && item.contactStatus === "Client")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                    "Solution Provider": revenueData
                      .filter((item) => item && item.month === "Apr" && item.contactStatus === "Solution Provider")
                      .reduce((sum, item) => sum + (item?.revenue || 0), 0),
                  },
                ]}
                categories={{
                  Client: {
                    label: "Client",
                    color: "hsl(var(--chart-1))",
                  },
                  "Solution Provider": {
                    label: "Solution Provider",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                index="month"
                valueFormatter={(value) => formatCurrency(value)}
              />
            </ChartCard>
          </TabsContent>

          <TabsContent value="transactions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Transactions</CardTitle>
                <CardDescription>Detailed list of all revenue transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50 font-medium">
                          <th className="h-10 px-4 text-left">Company</th>
                          <th className="h-10 px-4 text-left">City</th>
                          <th className="h-10 px-4 text-left">Medium</th>
                          <th className="h-10 px-4 text-left">Channel</th>
                          <th className="h-10 px-4 text-left">Property</th>
                          <th className="h-10 px-4 text-left">Variant</th>
                          <th className="h-10 px-4 text-left">Month</th>
                          <th className="h-10 px-4 text-left">Year</th>
                          <th className="h-10 px-4 text-right">Revenue</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredData.map((item) => (
                          <tr key={item.id} className="border-b">
                            <td className="p-4">{item.companyName}</td>
                            <td className="p-4">{item.city}</td>
                            <td className="p-4">{item.medium}</td>
                            <td className="p-4">{item.channel}</td>
                            <td className="p-4">{item.property}</td>
                            <td className="p-4">{item.variant}</td>
                            <td className="p-4">{item.month}</td>
                            <td className="p-4">{item.year}</td>
                            <td className="p-4 text-right">{formatCurrency(item.revenue)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Drill Down Dialog */}
      <DrillDownDialog
        open={drillDownOpen}
        onOpenChange={setDrillDownOpen}
        title={drillDownTitle}
        data={drillDownData}
        type={drillDownType}
        revenueData={revenueData}
      />
    </div>
  )
}
