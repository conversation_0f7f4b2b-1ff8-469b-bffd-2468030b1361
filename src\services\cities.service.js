import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class CityService {
  async getAllCities(
    params = {
      page: 1,
      limit: 100,
      search: "",
      state: "",
      country: "",
      status: "",
    }
  ) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.state) queryParams.append("state", params.state);
      if (params.country) queryParams.append("country", params.country);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/cities?${queryString}`
        : `${API_URL}/cities`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const citiesData = Array.isArray(response.data) ? response.data : [];

      return {
        cities: citiesData,
      };
    } catch (error) {
      console.error("Error fetching cities:", error);
      throw error;
    }
  }

  async getCityById(id) {
    try {
      const response = await axios.get(`${API_URL}/cities/${id}`);
      const city = response.data;
      return city;
    } catch (error) {
      console.error(`Error fetching city with ID ${id}:`, error);
      throw error;
    }
  }

  async createCity(cityData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: cityData.name,
        state: cityData.state,
        country: cityData.country || "India",
        status: cityData.status || "active", // Send status directly
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/cities`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating city:", error);
      throw error;
    }
  }

  async updateCity(id, cityData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: cityData.name,
        state: cityData.state,
        country: cityData.country || "India",
        status: cityData.status || "active", // Send status directly
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/cities/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating city with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteCity(id) {
    try {
      const response = await axios.delete(`${API_URL}/cities/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting city with ID ${id}:`, error);
      throw error;
    }
  }

  async changeCityStatus(id, status) {
    try {
      const response = await axios.patch(`${API_URL}/cities/${id}/status`, {
        status: status, // "active" or "inactive"
      });
      return response.data;
    } catch (error) {
      console.error(`Error changing city status for ID ${id}:`, error);
      throw error;
    }
  }

  async getCitiesByState(state) {
    try {
      const response = await axios.get(`${API_URL}/cities/by-state/${state}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching cities by state ${state}:`, error);
      throw error;
    }
  }

  async getCitiesByCountry(country) {
    try {
      const response = await axios.get(
        `${API_URL}/cities/by-country/${country}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching cities by country ${country}:`, error);
      throw error;
    }
  }

  async searchCities(searchTerm) {
    try {
      const response = await axios.get(`${API_URL}/cities/search`, {
        params: { q: searchTerm },
      });
      return response.data;
    } catch (error) {
      console.error(`Error searching cities with term ${searchTerm}:`, error);
      throw error;
    }
  }

  async getStates(country = "India") {
    try {
      const response = await axios.get(`${API_URL}/cities/states`, {
        params: { country },
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching states for country ${country}:`, error);
      throw error;
    }
  }

  async getCountries() {
    try {
      const response = await axios.get(`${API_URL}/cities/countries`);
      return response.data;
    } catch (error) {
      console.error("Error fetching countries:", error);
      throw error;
    }
  }

  async bulkImportCities(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/cities/insert-cities`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing cities from Excel:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const cityService = new CityService();
export default cityService;
