"use client";

import { Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "./contexts/auth-context.jsx";
import { Toaster } from "./components/ui/toaster.jsx";

// Layout
import Layout from "./components/layout/Layout.jsx";

// Pages
import Dashboard from "./pages/dashboard/Dashboard.jsx";
import LoginPage from "./pages/login/Login.jsx";
import UsersPage from "./pages/users/Users.jsx";
import ProductionLists from "./pages/masters/production-lists/ProductionLists.jsx";
import Roles from "./pages/masters/roles/Roles.jsx";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, isLoading } = useAuth();
  console.log("user", user);
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        Loading...
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

function App() {
  return (
    <>
      <Routes>
        <Route path="/login" element={<LoginPage />} />

        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route path="masters/users" element={<UsersPage />} />
          <Route
            path="masters/production-lists"
            element={<ProductionLists />}
          />
          <Route path="masters/roles" element={<Roles />} />
        </Route>
      </Routes>
      <Toaster />
    </>
  );
}

export default App;
