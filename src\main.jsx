import React from "react"
import <PERSON><PERSON><PERSON><PERSON> from "react-dom/client"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom"
import App from "./App.jsx"
import "./index.css"
import { ThemeProvider } from "./components/theme-provider.jsx"
import { AuthProvider } from "./contexts/auth-context.jsx"

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <AuthProvider>
          <App />
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
